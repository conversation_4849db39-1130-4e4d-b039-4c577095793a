export type MethodResult = CommandResult | string | void;
export declare class CommandResult {
    result: string;
    constructor(result: string);
    toString(): string;
    getDisplayResults(): string;
}
export declare abstract class TestCommandResult extends CommandResult {
    protected jsonResult: string;
    protected sarifResult: string;
    protected jsonData: {};
    getJsonResult(): string;
    getSarifResult(): string;
    getJsonData(): Record<string, unknown>;
    static createHumanReadableTestCommandResult(humanReadableResult: string, jsonResult: string, sarifResult?: string, jsonData?: Record<string, unknown>): HumanReadableTestCommandResult;
    static createJsonTestCommandResult(stdout: string, jsonResult?: string, sarifResult?: string, jsonPayload?: Record<string, unknown>): JsonTestCommandResult;
}
declare class HumanReadableTestCommandResult extends TestCommandResult {
    protected jsonResult: string;
    protected sarifResult: string;
    protected jsonData: {};
    constructor(humanReadableResult: string, jsonResult: string, sarifResult?: string, jsonData?: Record<string, unknown>);
    getJsonResult(): string;
    getSarifResult(): string;
    getJsonData(): Record<string, unknown>;
}
declare class JsonTestCommandResult extends TestCommandResult {
    constructor(stdout: string, jsonResult?: string, sarifResult?: string, jsonData?: Record<string, unknown>);
    getJsonResult(): string;
    getSarifResult(): string;
}
export interface IgnoreMetadata {
    reason: string;
    expires: Date;
    created: Date;
}
export interface IgnoreRulePathData {
    [path: string]: IgnoreMetadata;
}
export {};
