const mongoose = require('mongoose');

/**
 * Audit Log Schema
 * Comprehensive audit trail for all admin actions and security events
 */

const auditLogSchema = new mongoose.Schema({
  // Event Information
  eventType: {
    type: String,
    required: true,
    enum: [
      'login', 'logout', 'login_failed', 'password_change', 'password_reset',
      'mfa_enabled', 'mfa_disabled', 'user_created', 'user_updated', 'user_deleted',
      'role_changed', 'permission_granted', 'permission_revoked',
      'content_created', 'content_updated', 'content_deleted', 'content_published',
      'system_config_changed', 'backup_created', 'backup_restored',
      'security_violation', 'suspicious_activity', 'rate_limit_exceeded',
      'session_expired', 'account_locked', 'account_unlocked'
    ]
  },
  
  // User Information
  userId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: function() {
      return this.eventType !== 'system_event';
    }
  },
  username: {
    type: String,
    required: function() {
      return this.userId != null;
    }
  },
  userRole: {
    type: String,
    enum: ['super_admin', 'admin', 'editor', 'viewer', 'system']
  },

  // Target Information (what was acted upon)
  targetType: {
    type: String,
    enum: ['user', 'content', 'system', 'session', 'file', 'configuration']
  },
  targetId: {
    type: String // Can be ObjectId or other identifier
  },
  targetName: String,

  // Action Details
  action: {
    type: String,
    required: true,
    enum: ['create', 'read', 'update', 'delete', 'login', 'logout', 'grant', 'revoke', 'enable', 'disable', 'lock', 'unlock']
  },
  resource: {
    type: String,
    required: true
  },
  description: {
    type: String,
    required: true,
    maxlength: 1000
  },

  // Request Information
  ipAddress: {
    type: String,
    required: true,
    validate: {
      validator: function(v) {
        // Basic IP validation (IPv4 and IPv6)
        return /^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$|^([0-9a-fA-F]{1,4}:){7}[0-9a-fA-F]{1,4}$/.test(v);
      },
      message: 'Invalid IP address format'
    }
  },
  userAgent: {
    type: String,
    maxlength: 500
  },
  sessionId: String,

  // HTTP Request Details
  method: {
    type: String,
    enum: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS', 'HEAD']
  },
  endpoint: {
    type: String,
    maxlength: 200
  },
  statusCode: Number,
  responseTime: Number, // in milliseconds

  // Data Changes (for update operations)
  changes: {
    before: mongoose.Schema.Types.Mixed,
    after: mongoose.Schema.Types.Mixed,
    fields: [String] // List of changed fields
  },

  // Security Information
  severity: {
    type: String,
    enum: ['low', 'medium', 'high', 'critical'],
    default: 'low'
  },
  riskScore: {
    type: Number,
    min: 0,
    max: 100,
    default: 0
  },
  
  // Geolocation (if available)
  location: {
    country: String,
    region: String,
    city: String,
    coordinates: {
      latitude: Number,
      longitude: Number
    }
  },

  // Additional Metadata
  metadata: {
    type: mongoose.Schema.Types.Mixed,
    default: {}
  },

  // Compliance and Retention
  retentionDate: {
    type: Date,
    default: function() {
      // Default retention: 7 years for compliance
      return new Date(Date.now() + 7 * 365 * 24 * 60 * 60 * 1000);
    }
  },
  complianceFlags: [{
    type: String,
    enum: ['gdpr', 'hipaa', 'sox', 'pci_dss', 'iso27001']
  }],

  // System Information
  serverInstance: {
    type: String,
    default: process.env.SERVER_INSTANCE || 'default'
  },
  applicationVersion: {
    type: String,
    default: process.env.APP_VERSION || '1.0.0'
  },

  // Timestamps
  timestamp: {
    type: Date,
    default: Date.now,
    required: true
  },
  
  // Processing Status
  processed: {
    type: Boolean,
    default: false
  },
  alertsSent: [{
    type: String,
    timestamp: {
      type: Date,
      default: Date.now
    }
  }]
}, {
  timestamps: true,
  // Optimize for time-series queries
  timeseries: {
    timeField: 'timestamp',
    metaField: 'metadata',
    granularity: 'hours'
  }
});

// Indexes for performance
auditLogSchema.index({ timestamp: -1 }); // Most recent first
auditLogSchema.index({ userId: 1, timestamp: -1 }); // User activity timeline
auditLogSchema.index({ eventType: 1, timestamp: -1 }); // Event type queries
auditLogSchema.index({ ipAddress: 1, timestamp: -1 }); // IP-based queries
auditLogSchema.index({ severity: 1, timestamp: -1 }); // Security monitoring
auditLogSchema.index({ targetType: 1, targetId: 1, timestamp: -1 }); // Target-based queries
auditLogSchema.index({ retentionDate: 1 }); // Cleanup queries
auditLogSchema.index({ processed: 1, severity: 1 }); // Alert processing

// Compound indexes for common queries
auditLogSchema.index({ 
  eventType: 1, 
  userId: 1, 
  timestamp: -1 
}); // User event history

auditLogSchema.index({ 
  ipAddress: 1, 
  eventType: 1, 
  timestamp: -1 
}); // IP activity analysis

// Text index for searching descriptions
auditLogSchema.index({ 
  description: 'text',
  targetName: 'text',
  username: 'text'
});

// Static methods
auditLogSchema.statics.logEvent = async function(eventData) {
  try {
    const auditEntry = new this({
      ...eventData,
      timestamp: new Date()
    });

    await auditEntry.save();
    
    // Trigger alerts for high-severity events
    if (eventData.severity === 'high' || eventData.severity === 'critical') {
      // This would integrate with your alerting system
      console.warn('High-severity audit event:', eventData);
    }

    return auditEntry;
  } catch (error) {
    console.error('Failed to log audit event:', error);
    throw error;
  }
};

auditLogSchema.statics.getUserActivity = function(userId, limit = 50) {
  return this.find({ userId })
    .sort({ timestamp: -1 })
    .limit(limit)
    .populate('userId', 'username email role');
};

auditLogSchema.statics.getSecurityEvents = function(timeframe = 24) {
  const since = new Date(Date.now() - timeframe * 60 * 60 * 1000);
  
  return this.find({
    timestamp: { $gte: since },
    severity: { $in: ['high', 'critical'] }
  }).sort({ timestamp: -1 });
};

auditLogSchema.statics.getFailedLogins = function(timeframe = 1) {
  const since = new Date(Date.now() - timeframe * 60 * 60 * 1000);
  
  return this.aggregate([
    {
      $match: {
        eventType: 'login_failed',
        timestamp: { $gte: since }
      }
    },
    {
      $group: {
        _id: '$ipAddress',
        count: { $sum: 1 },
        lastAttempt: { $max: '$timestamp' },
        usernames: { $addToSet: '$username' }
      }
    },
    {
      $sort: { count: -1 }
    }
  ]);
};

auditLogSchema.statics.cleanupExpiredLogs = async function() {
  const result = await this.deleteMany({
    retentionDate: { $lt: new Date() }
  });
  
  console.log(`Cleaned up ${result.deletedCount} expired audit logs`);
  return result;
};

// Instance methods
auditLogSchema.methods.markAsProcessed = function() {
  this.processed = true;
  return this.save();
};

auditLogSchema.methods.addAlert = function(alertType) {
  this.alertsSent.push({
    type: alertType,
    timestamp: new Date()
  });
  return this.save();
};

// Pre-save middleware
auditLogSchema.pre('save', function(next) {
  // Calculate risk score based on event type and severity
  if (this.isNew) {
    let riskScore = 0;
    
    // Base score by event type
    const riskByEventType = {
      'login_failed': 10,
      'security_violation': 50,
      'suspicious_activity': 40,
      'user_deleted': 30,
      'role_changed': 25,
      'system_config_changed': 35,
      'account_locked': 20
    };
    
    riskScore += riskByEventType[this.eventType] || 5;
    
    // Severity multiplier
    const severityMultiplier = {
      'low': 1,
      'medium': 2,
      'high': 3,
      'critical': 4
    };
    
    riskScore *= severityMultiplier[this.severity] || 1;
    
    this.riskScore = Math.min(riskScore, 100);
  }
  
  next();
});

module.exports = mongoose.model('AuditLog', auditLogSchema);
