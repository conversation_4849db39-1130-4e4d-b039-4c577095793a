import { SEVERITY } from '../../../../snyk-test/common';
import { IacProjectType } from '../../../constants';
import { SnykIacTestError } from '../errors';
import * as PolicyEngineTypes from './policy-engine';
export declare function mapSnykIacTestOutputToTestOutput(snykIacOutput: SnykIacTestOutput): TestOutput;
export interface TestOutput {
    results?: Results;
    errors?: SnykIacTestError[];
    warnings?: SnykIacTestError[];
    settings: Settings;
}
export interface SnykIacTestOutput {
    results?: Results;
    rawResults?: PolicyEngineTypes.Results;
    errors?: ScanError[];
    warnings?: ScanError[];
    settings: Settings;
}
export interface Results {
    resources?: Resource[];
    vulnerabilities?: Vulnerability[];
    passedVulnerabilities?: Vulnerability[];
    metadata: Metadata;
    scanAnalytics: ScanAnalytics;
}
export interface Metadata {
    projectName: string;
    projectPublicId: string;
    ignoredCount: number;
}
export interface Settings {
    org: string;
    ignoreSettings: IgnoreSettings;
}
export interface IgnoreSettings {
    adminOnly: boolean;
    disregardFilesystemIgnores: boolean;
    reasonRequired: boolean;
}
export interface ScanAnalytics {
    suppressedResults?: Record<string, string[]>;
    ignoredCount: number;
}
export interface Vulnerability {
    rule: Rule;
    message: string;
    remediation: string;
    severity: SEVERITY;
    ignored: boolean;
    resource: Resource;
    context?: {
        evidence?: string;
    };
}
export interface Rule {
    id: string;
    title: string;
    description: string;
    references?: string;
    labels?: string[];
    category?: string;
    documentation?: string;
    isGeneratedByCustomRule?: boolean;
}
export interface Resource {
    id: string;
    type: string;
    kind: ResourceKind;
    formattedPath: string;
    path?: any[];
    file?: string;
    line?: number;
    column?: number;
}
export type ResourceKind = IacProjectType | PolicyEngineTypes.State.InputTypeEnum;
export interface ScanError {
    message: string;
    code: number;
    fields?: Record<string, string>;
}
