import { IacFileTypes, IacProjectType, IacProjectTypes } from '../../../../../lib/iac/constants';
import { SEVERITY } from '../../../../../lib/snyk-test/common';
import { AnnotatedIssue, IgnoreSettings, TestResult } from '../../../../../lib/snyk-test/legacy';
import { IacFileInDirectory, Options, TestOptions, PolicyOptions } from '../../../../../lib/types';
export interface IacFileData extends IacFileInDirectory {
    fileContent: string;
}
export declare const VALID_FILE_TYPES: string[];
export declare const VALID_TERRAFORM_FILE_TYPES: string[];
export interface IacFileParsed extends IacFileData {
    jsonContent: Record<string, unknown> | TerraformScanInput;
    projectType: IacProjectType;
    engineType: EngineType;
    docId?: number;
}
export interface IacFileParseFailure extends IacFileData {
    jsonContent: null;
    engineType: null;
    failureReason: string;
    err: Error;
}
export type ParsingResults = {
    parsedFiles: Array<IacFileParsed>;
    failedFiles: Array<IacFileParseFailure>;
};
export interface IacFileScanResult extends IacFileParsed {
    violatedPolicies: PolicyMetadata[];
}
export interface IacShareResultsFormat {
    projectName: string;
    targetFile: string;
    filePath: string;
    fileType: IacFileTypes;
    projectType: IacProjectType;
    violatedPolicies: PolicyMetadata[];
}
export interface FormattedTestMeta {
    isPrivate: boolean;
    isLicensesEnabled: boolean;
    org: string;
    orgPublicId: string;
    ignoreSettings?: IgnoreSettings | null;
    projectId?: string;
    policy?: string;
    gitRemoteUrl?: string;
}
export type FormattedResult = {
    result: {
        cloudConfigResults: Array<PolicyMetadata>;
        projectType: IacProjectTypes;
    };
    meta: FormattedTestMeta;
    filesystemPolicy: boolean;
    vulnerabilities: AnnotatedIssue[];
    dependencyCount: number;
    licensesPolicy: object | null;
    ignoreSettings: IgnoreSettings | null;
    targetFile: string;
    projectName: string;
    org: string;
    policy: string;
    isPrivate: boolean;
    targetFilePath: string;
    packageManager: IacProjectType;
};
export type IacCustomPolicies = Record<string, {
    severity?: string;
}>;
export declare enum RulesOrigin {
    Local = "local",
    Remote = "remote",
    Internal = "internal"
}
export interface IacCustomRules {
    isEnabled?: boolean;
    ociRegistryURL?: string;
    ociRegistryTag?: string;
}
export interface IacEntitlements {
    infrastructureAsCode?: boolean;
    iacDrift?: boolean;
    iacCustomRulesEntitlement?: boolean;
}
export interface IacOrgSettings {
    meta: TestMeta;
    customPolicies: IacCustomPolicies;
    customRules?: IacCustomRules;
    entitlements?: IacEntitlements;
}
export interface TestMeta {
    org: string;
    orgPublicId: string;
    ignoreSettings?: IgnoreSettings | null;
    projectId?: string;
    gitRemoteUrl?: string;
}
export interface OpaWasmInstance {
    evaluate: (data: Record<string, any>) => {
        results: PolicyMetadata[];
    };
    setData: (data: Record<string, any>) => void;
}
export type SafeAnalyticsOutput = Omit<IacFileParsed | IacFileParseFailure, 'fileContent' | 'jsonContent' | 'engineType'>;
export declare enum EngineType {
    Kubernetes = 0,
    Terraform = 1,
    CloudFormation = 2,
    ARM = 3,
    Custom = 4
}
export interface PolicyMetadata {
    id?: string;
    publicId: string;
    type?: string;
    subType: string;
    title: string;
    documentation?: string;
    isGeneratedByCustomRule?: boolean;
    description?: string;
    severity: SEVERITY | 'none';
    msg: string;
    issue: string;
    impact: string;
    resolve: string;
    references: string[];
    remediation?: Partial<Record<'terraform' | 'cloudformation' | 'arm' | 'kubernetes', string>>;
    docId?: number;
}
export type IaCTestFlags = Pick<Options & TestOptions & PolicyOptions, 'org' | 'insecure' | 'debug' | 'experimental' | 'detectionDepth' | 'severityThreshold' | 'json' | 'sarif' | 'report' | 'target-reference' | 'var-file' | 'ignore-policy' | 'policy-path' | 'tags' | 'remote-repo-url' | 'target-name'> & {
    'json-file-output'?: string;
    'sarif-file-output'?: string;
    v?: boolean;
    version?: boolean;
    h?: boolean;
    help?: 'help';
    q?: boolean;
    quiet?: boolean;
    path?: string;
    rules?: string;
    'custom-rules'?: boolean;
    'snyk-cloud-environment'?: string;
    'project-tags'?: string;
    'project-environment'?: string;
    'project-lifecycle'?: string;
    'project-business-criticality'?: string;
    'iac-test-output-file'?: string;
} & TerraformPlanFlags;
interface TerraformPlanFlags {
    scan?: TerraformPlanScanMode;
}
export declare enum TerraformPlanScanMode {
    DeltaScan = "resource-changes",
    FullScan = "planned-values"
}
export interface TerraformPlanResource {
    address: string;
    mode: string;
    type: string;
    name: string;
    values: Record<string, unknown>;
    index: number | string;
}
export interface TerraformPlanResourceChange extends Omit<TerraformPlanResource, 'values'> {
    change: {
        actions: ResourceActions;
        before: Record<string, unknown> | null;
        after: Record<string, unknown> | null;
    };
}
export interface TerraformPlanJson {
    resource_changes: Array<TerraformPlanResourceChange>;
    configuration: {
        root_module: {
            resources: Array<TerraformPlanReferencedResource>;
        };
    };
}
export interface TerraformPlanReferencedResource extends TerraformPlanResource {
    expressions?: Record<string, TerraformPlanExpression>;
}
export interface TerraformPlanExpression {
    references: Array<string>;
}
export interface TerraformScanInput {
    resource: Record<string, Record<string, unknown>>;
    data: Record<string, Record<string, unknown>>;
}
export type ResourceActions = ['no-op'] | ['create'] | ['read'] | ['update'] | ['delete', 'create'] | ['create', 'delete'] | ['delete'];
export declare const VALID_RESOURCE_ACTIONS_FOR_DELTA_SCAN: ResourceActions[];
export declare const VALID_RESOURCE_ACTIONS_FOR_FULL_SCAN: ResourceActions[];
export declare enum IaCErrorCodes {
    FailedToInitLocalCacheError = 1000,
    FailedToCleanLocalCacheError = 1001,
    FailedToDownloadRulesError = 1002,
    FailedToExtractCustomRulesError = 1003,
    InvalidCustomRules = 1004,
    InvalidCustomRulesPath = 1005,
    InvalidVarFilePath = 1006,
    NoFilesToScanError = 1010,
    FailedToLoadFileError = 1011,
    CurrentWorkingDirectoryTraversalError = 1012,
    UnsupportedFileTypeError = 1020,
    InvalidJsonFileError = 1021,
    InvalidYamlFileError = 1022,
    FailedToDetectJsonConfigError = 1023,
    FailedToDetectYamlConfigError = 1024,
    MissingRequiredFieldsInKubernetesYamlError = 1031,
    FailedToParseHelmError = 1032,
    FailedToParseTerraformFileError = 1040,
    FailedToExtractResourcesInTerraformPlanError = 1052,
    FailedToBuildPolicyEngine = 1060,
    FailedToExecutePolicyEngine = 1061,
    FailedToFormatResults = 1070,
    FailedToExtractLineNumberError = 1071,
    FailedToGetIacOrgSettingsError = 1080,
    FlagError = 1090,
    FlagValueError = 1091,
    UnsupportedEntitlementFlagError = 1092,
    FeatureFlagError = 1093,
    InvalidArgumentError = 1094,
    FailedToExecuteCustomRulesError = 1100,
    FailedToPullCustomBundleError = 1101,
    FailedToBuildOCIArtifactError = 1102,
    InvalidRemoteRegistryURLError = 1103,
    InvalidManifestSchemaVersionError = 1104,
    UnsupportedFeatureFlagPullError = 1105,
    UnsupportedEntitlementPullError = 1106,
    InvalidServiceError = 1110,
    InvalidUserRulesBundlePathError = 1130,
    InvalidUserPolicyEnginePathError = 1140,
    FailedToDownloadPolicyEngineError = 1141,
    FailedToCachePolicyEngineError = 1142,
    PolicyEngineScanError = 1150,
    NoPaths = 2000,
    CwdTraversal = 2003,
    NoBundle = 2004,
    OpenBundle = 2005,
    InvalidSeverityThreshold = 2006,
    Scan = 2100,
    UnableToRecognizeInputType = 2101,
    UnsupportedInputType = 2102,
    UnableToResolveLocation = 2103,
    UnrecognizedFileExtension = 2104,
    FailedToParseInput = 2105,
    InvalidInput = 2106,
    UnableToReadFile = 2107,
    UnableToReadDir = 2108,
    UnableToReadStdin = 2109,
    FailedToLoadRegoAPI = 2110,
    FailedToLoadRules = 2111,
    FailedToCompile = 2112,
    UnableToReadPath = 2113,
    NoLoadableInput = 2114,
    FailedToMakeResourcesResolvers = 2115,
    ResourcesResolverError = 2116,
    TestLimitReached = 2117,
    FailedToProcessResults = 2200,
    EntitlementNotEnabled = 2201,
    ReadSettings = 2202,
    SubmoduleLoadingError = 3000,
    MissingRemoteSubmodulesError = 3001,
    EvaluationError = 3002,
    MissingTermError = 3003
}
export interface TestReturnValue {
    results: TestResult | TestResult[];
    failures?: IacFileInDirectory[];
    ignoreCount: number;
}
export interface OCIRegistryURLComponents {
    registryBase: string;
    repo: string;
    tag: string;
}
export declare enum PerformanceAnalyticsKey {
    InitLocalCache = "cache-init-ms",
    FileLoading = "file-loading-ms",
    FileParsing = "file-parsing-ms",
    FileScanning = "file-scanning-ms",
    OrgSettings = "org-settings-ms",
    CustomSeverities = "custom-severities-ms",
    ResultFormatting = "results-formatting-ms",
    UsageTracking = "usage-tracking-ms",
    CacheCleanup = "cache-cleanup-ms",
    Total = "total-iac-ms"
}
export interface ShareResultsOutput {
    projectPublicIds: {
        [targetFile: string]: string;
    };
    gitRemoteUrl?: string;
}
export {};
