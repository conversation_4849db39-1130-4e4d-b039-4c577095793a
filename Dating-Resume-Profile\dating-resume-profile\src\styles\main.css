#edit-name,
#edit-tagline,
#edit-objective,
#edit-qualifications,
#edit-skills,
#edit-hobbies,
#edit-growth,
#edit-looking-for {
    width: 100%;
    box-sizing: border-box;
    background: #111 !important;
    color: #fff !important;
    border: 2px solid #bcd0ee !important;
    border-radius: 8px;
    padding: 10px;
    font-size: 1em;
    box-shadow: none;
}
#edit-objective,
#edit-looking-for,
#edit-qualifications,
#edit-skills,
#edit-hobbies,
#edit-growth {
    min-height: 120px;
    resize: vertical;
}
.modal-content input,
.modal-content textarea,
#edit-objective,
#edit-looking-for {
    width: 100%;
    min-height: 48px;
    box-sizing: border-box;
}
.modal-content textarea,
#edit-objective,
#edit-looking-for {
    min-height: 120px;
    resize: vertical;
}
.modal-content h2 {
    color: #fff;
}
.modal-content input,
.modal-content textarea,
#edit-objective,
#edit-looking-for {
    background: #111 !important;
    color: #fff !important;
    border: 2px solid #bcd0ee !important;
    border-radius: 8px;
    padding: 10px;
    font-size: 1em;
    box-shadow: none;
}
#edit-objective,
#edit-looking-for {
    background: #111 !important;
    color: #fff !important;
    border: 1px solid #444;
    border-radius: 8px;
    padding: 10px;
    font-size: 1em;
    box-shadow: none;
}
.modal-content input,
.modal-content textarea {
    background: #111 !important;
    color: #fff !important;
    border: 1px solid #444;
    border-radius: 8px;
    padding: 10px;
    font-size: 1em;
    box-shadow: none;
}
.modal-content label {
    color: #fff;
}
.modal-content input,
.modal-content textarea {
    background: #111;
    color: #fff;
    border: 1px solid #444;
    border-radius: 8px;
    padding: 10px;
    font-size: 1em;
}
#captcha-form button {
    background: #000;
    color: #fff;
    border-radius: 16px;
    font-weight: bold;
    padding: 16px 32px;
    border: none;
    font-size: 1.2em;
    box-shadow: 0 2px 8px rgba(0,0,0,0.08);
    cursor: pointer;
    transition: background 0.2s;
}
#captcha-form button:hover {
    background: #222;
}
.modal-content label,
.modal-content,
.modal-content input,
.modal-content textarea {
    color: #fff;
}
/* Contact Section Styles */
.contact-section {
    margin-top: 32px;
    padding: 24px 0;
}

#contact-details {
    background: #fff;
    color: #222;
    border-radius: 16px;
    box-shadow: 0 2px 12px rgba(0,0,0,0.10);
    padding: 24px;
    max-width: 350px;
    margin: 32px auto;
}

/* Footer Styles */
.footer {
    background: rgba(0, 0, 0, 0.1);
    color: #fff;
    padding: 20px 0;
    margin-top: 40px;
    text-align: center;
}

.footer p {
    margin: 0;
    font-size: 0.9rem;
    opacity: 0.8;
}

/* Profile Meta (Age, Location) */
.profile-meta {
    margin-top: 10px;
    font-size: 1.08rem;
    color: #fff;
    font-weight: 500;
    letter-spacing: 0.5px;
}
.profile-age, .profile-location {
    font-style: italic;
}
/* Romantic Resume Info Section Styles */
.romantic-resume-info {
    max-width: 600px;
    margin: 32px auto 0 auto;
    padding: 0 12px;
}
.toggle-info {
    width: 100%;
    background: linear-gradient(90deg, #e0c3fc 0%, #8ec5fc 100%);
    color: #fff;
    border: none;
    border-radius: 10px;
    font-size: 1.15rem;
    font-weight: 600;
    padding: 16px 0;
    cursor: pointer;
    box-shadow: 0 2px 8px rgba(80,80,160,0.10);
    color: #fff;
    transition: background 0.2s, transform 0.2s;
    letter-spacing: 0.5px;
}
.toggle-info:hover {
    background: linear-gradient(90deg, #8ec5fc 0%, #e0c3fc 100%);
    transform: scale(1.02);
}
.resume-content {
    margin-top: 0;
    animation: fadeInUp 0.5s cubic-bezier(.39,.575,.565,1.000);
}
.resume-content.hidden {
    display: none;
}
.resume-card {
    background: #000;
    border-radius: 16px;
    box-shadow: 0 4px 24px rgba(255, 255, 255, 0.1);
    padding: 32px 24px 24px 24px;
    margin-top: 0;
    color: #fff;
}
.resume-card h2 {
    font-size: 1.5rem;
    margin-bottom: 12px;
}
.resume-card h3 {
    font-size: 1.2rem;
    margin-top: 18px;
    margin-bottom: 8px;
}
.resume-card h4 {
    font-size: 1.08rem;
    margin-top: 16px;
    margin-bottom: 6px;
}
.resume-card ul {
    margin: 8px 0 16px 0;
    font-size: 1rem;
}
.resume-card li {
    margin-bottom: 6px;
}
.resume-card blockquote {
    background: #181818;
    border-left: 4px solid #fff;
    margin: 10px 0 10px 0;
    padding: 10px 16px;
    font-style: italic;
    border-radius: 6px;
}
/* Add your CSS styles here */

/* Global Responsive Utilities */
* {
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Arial, sans-serif;
    background: linear-gradient(135deg, #e0c3fc 0%, #8ec5fc 100%);
    margin: 0;
    padding: 0;
    min-height: 100vh;
    overflow-x: hidden;
}

/* Ensure images are responsive */
img {
    max-width: 100%;
    height: auto;
}

/* Section Header Styles */
.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 28px;
    margin-bottom: 10px;
    position: relative;
}

.section-header h2 {
    margin: 0;
    color: #fff;
    font-size: 1.15rem;
    font-weight: 600;
    letter-spacing: 0.5px;
}

.section-actions {
    display: flex;
    gap: 8px;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.section-header:hover .section-actions {
    opacity: 1;
}

/* Section Action Buttons */
.btn-section-edit,
.btn-section-add,
.btn-section-collapse {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 6px;
    padding: 4px 8px;
    color: #fff;
    cursor: pointer;
    transition: all 0.2s ease;
    font-size: 0.8rem;
    min-width: 28px;
    height: 28px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.btn-section-edit:hover {
    background: rgba(142, 197, 252, 0.3);
    border-color: #8ec5fc;
    transform: scale(1.05);
}

.btn-section-add:hover {
    background: rgba(34, 197, 94, 0.3);
    border-color: #22c55e;
    transform: scale(1.05);
}

.btn-section-collapse:hover {
    background: rgba(156, 163, 175, 0.3);
    border-color: #9ca3af;
    transform: scale(1.05);
}

/* List Item Styles */
.profile-details ul li {
    display: flex;
    justify-content: space-between;
    align-items: center;
    position: relative;
    min-height: 36px;
}

.item-text {
    flex: 1;
    margin-right: 8px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.item-actions {
    display: flex;
    gap: 4px;
    opacity: 0;
    transition: opacity 0.3s ease;
    flex-shrink: 0;
    margin-left: 8px;
}

.list-group-item:hover .item-actions {
    opacity: 1;
}

.list-group-item:hover .item-text {
    white-space: normal;
    overflow: visible;
}

/* Item Action Buttons */
.btn-item-edit,
.btn-item-remove {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 4px;
    padding: 2px 6px;
    color: #fff;
    cursor: pointer;
    transition: all 0.2s ease;
    font-size: 0.7rem;
    min-width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.btn-item-edit:hover {
    background: rgba(142, 197, 252, 0.3);
    border-color: #8ec5fc;
    transform: scale(1.1);
}

.btn-item-remove:hover {
    background: rgba(239, 68, 68, 0.3);
    border-color: #ef4444;
    transform: scale(1.1);
}

/* Collapsible Sections */
.collapsible-section {
    transition: max-height 0.3s ease, opacity 0.3s ease;
    overflow: hidden;
}

.collapsible-section.collapsed {
    max-height: 0;
    opacity: 0;
    margin: 0;
    padding: 0;
}

/* Editable Text Styles */
.editable-text {
    position: relative;
    cursor: text;
    transition: background-color 0.2s ease;
}

.editable-text:hover {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 4px;
    padding: 4px;
    margin: -4px;
}

/* Floating Action Button System */
.fab-container {
    position: fixed;
    bottom: 24px;
    right: 24px;
    z-index: 1000;
}

.fab {
    width: 56px;
    height: 56px;
    border-radius: 50%;
    border: none;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2rem;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
}

.fab-main {
    background: linear-gradient(135deg, #8ec5fc 0%, #e0c3fc 100%);
    color: #fff;
    transform: scale(1);
}

.fab-main:hover {
    transform: scale(1.1);
    box-shadow: 0 6px 16px rgba(0, 0, 0, 0.4);
}

.fab-main.active {
    transform: rotate(45deg) scale(1.1);
}

.fab-secondary {
    background: rgba(255, 255, 255, 0.9);
    color: #333;
    width: 48px;
    height: 48px;
    font-size: 1rem;
    margin-bottom: 12px;
    opacity: 0;
    transform: scale(0) translateY(20px);
    pointer-events: none;
}

.fab-menu {
    position: absolute;
    bottom: 70px;
    right: 4px;
    display: flex;
    flex-direction: column;
    align-items: center;
}

.fab-menu.active .fab-secondary {
    opacity: 1;
    transform: scale(1) translateY(0);
    pointer-events: all;
}

.fab-menu.active .fab-secondary:nth-child(1) {
    transition-delay: 0.1s;
}

.fab-menu.active .fab-secondary:nth-child(2) {
    transition-delay: 0.15s;
}

.fab-menu.active .fab-secondary:nth-child(3) {
    transition-delay: 0.2s;
}

.fab-menu.active .fab-secondary:nth-child(4) {
    transition-delay: 0.25s;
}

.fab-secondary:hover {
    background: #fff;
    transform: scale(1.1) translateY(0);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

/* FAB Icons */
.fab-icon {
    display: inline-block;
    line-height: 1;
}

/* Quick Action Modal Styles */
.quick-add-content,
.quick-edit-content {
    max-width: 400px;
    padding: 24px;
}

.quick-add-content h3,
.quick-edit-content h3 {
    color: #fff;
    margin-bottom: 16px;
    font-size: 1.2rem;
}

.quick-add-actions,
.quick-edit-actions {
    display: flex;
    gap: 12px;
    justify-content: flex-end;
    margin-top: 16px;
}

.quick-add-actions .btn,
.quick-edit-actions .btn {
    padding: 8px 16px;
    font-size: 0.9rem;
}

/* Button Group Styles */
.button-group {
    display: flex;
    gap: 8px;
    margin-top: 16px;
    flex-wrap: wrap;
}

.button-group .btn {
    flex: 1;
    min-width: 120px;
}

/* Inline Edit Styles */
.inline-edit {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.3);
    border-radius: 4px;
    padding: 4px 8px;
    color: #fff;
    font-size: inherit;
    width: 100%;
    box-sizing: border-box;
}

.inline-edit:focus {
    outline: none;
    border-color: #8ec5fc;
    background: rgba(255, 255, 255, 0.15);
}

/* Primary Actions */
.primary-actions {
    margin: 32px 0;
    text-align: center;
}

/* Button Icons */
.btn-icon {
    margin-right: 6px;
    font-size: 0.9em;
}

/* Button Variants */
.btn-success {
    background: linear-gradient(90deg, #22c55e 0%, #16a34a 100%);
    color: #fff;
    border: none;
}

.btn-success:hover {
    background: linear-gradient(90deg, #16a34a 0%, #15803d 100%);
    transform: scale(1.04);
}

.btn-outline-primary {
    background: transparent;
    border: 2px solid #8ec5fc;
    color: #8ec5fc;
}

.btn-outline-primary:hover {
    background: #8ec5fc;
    color: #fff;
}

.btn-sm {
    padding: 6px 12px;
    font-size: 0.85rem;
}

/* Contact Actions */
.contact-actions {
    margin-top: 16px;
    display: flex;
    gap: 12px;
    justify-content: center;
    flex-wrap: wrap;
}

/* Button States */
.btn.hidden {
    display: none;
}

.btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none !important;
}

/* Button Hierarchy */
.btn-primary {
    z-index: 3;
}

.btn-secondary {
    z-index: 2;
}

.btn-outline-primary {
    z-index: 1;
}

/* Progressive Disclosure */
.progressive-disclosure {
    transition: all 0.3s ease;
}

.progressive-disclosure.collapsed {
    max-height: 0;
    opacity: 0;
    overflow: hidden;
    margin: 0;
    padding: 0;
}

.progressive-disclosure.expanded {
    max-height: 1000px;
    opacity: 1;
}

/* Context-aware buttons */
.context-menu {
    position: absolute;
    background: rgba(0, 0, 0, 0.9);
    border-radius: 8px;
    padding: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
    z-index: 1000;
    opacity: 0;
    transform: scale(0.8) translateY(-10px);
    transition: all 0.2s ease;
    pointer-events: none;
}

.context-menu.active {
    opacity: 1;
    transform: scale(1) translateY(0);
    pointer-events: all;
}

.context-menu-item {
    display: block;
    width: 100%;
    padding: 8px 12px;
    background: transparent;
    border: none;
    color: #fff;
    text-align: left;
    cursor: pointer;
    border-radius: 4px;
    transition: background 0.2s ease;
    font-size: 0.85rem;
}

.context-menu-item:hover {
    background: rgba(255, 255, 255, 0.1);
}

/* Advanced Controls */
.advanced-controls {
    margin-top: 16px;
    padding: 12px;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 8px;
    border: 1px solid rgba(255, 255, 255, 0.1);
    opacity: 0;
    max-height: 0;
    overflow: hidden;
    transition: all 0.3s ease;
}

.advanced-controls.visible {
    opacity: 1;
    max-height: 200px;
}

.advanced-controls h4 {
    color: #fff;
    font-size: 0.9rem;
    margin-bottom: 8px;
}

.advanced-controls .btn {
    margin: 4px;
    padding: 4px 8px;
    font-size: 0.8rem;
}

/* Edit Mode Styles */
.edit-mode .section-actions {
    opacity: 1;
}

.edit-mode .item-actions {
    opacity: 1;
}

.edit-mode .advanced-controls {
    opacity: 1;
    max-height: 200px;
}

/* Keyboard Shortcuts Modal */
.shortcuts-content {
    max-width: 600px;
    padding: 32px;
}

.shortcuts-content h3 {
    color: #fff;
    margin-bottom: 24px;
    text-align: center;
    font-size: 1.4rem;
}

.shortcuts-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 24px;
}

.shortcut-group h4 {
    color: #8ec5fc;
    font-size: 1rem;
    margin-bottom: 12px;
    border-bottom: 1px solid rgba(142, 197, 252, 0.3);
    padding-bottom: 4px;
}

.shortcut-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 6px 0;
    color: #fff;
    font-size: 0.9rem;
}

.shortcut-item kbd {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 4px;
    padding: 2px 6px;
    font-size: 0.8rem;
    color: #fff;
    font-family: monospace;
    margin-right: 8px;
}

.shortcut-item kbd + kbd {
    margin-left: 4px;
}

/* Keyboard Navigation Indicators */
.keyboard-focus {
    outline: 2px solid #8ec5fc;
    outline-offset: 2px;
}

/* Accessibility Improvements */
.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}

/* Focus Indicators */
button:focus-visible,
.btn:focus-visible {
    outline: 2px solid #8ec5fc;
    outline-offset: 2px;
}

/* High Contrast Mode Support */
@media (prefers-contrast: high) {
    .btn-section-edit,
    .btn-section-add,
    .btn-section-collapse,
    .btn-item-edit,
    .btn-item-remove {
        border-width: 2px;
        font-weight: bold;
    }
}

.profile-container {
    max-width: 800px;
    margin: 48px auto;
    background: #181818;
    border-radius: 18px;
    box-shadow: 0 12px 40px rgba(82,255,3,0.18);
    padding: 40px 32px 32px 32px;
    position: relative;
    color: #fff;
    width: 95%;
    box-sizing: border-box;
}

@keyframes fadeInUp {
    0% { opacity: 0; transform: translateY(40px); }
    100% { opacity: 1; transform: translateY(0); }
}

.profile-header {
    text-align: center;
    margin-bottom: 28px;
}

.profile-photo {
    width: 130px;
    height: 130px;
    border-radius: 50%;
    object-fit: cover;
    margin-bottom: 18px;
    border: 5px solid #8ec5fc;
    box-shadow: 0 2px 12px rgba(80,80,160,0.10);
    transition: transform 0.2s;
}
.profile-photo:hover {
    transform: scale(1.05) rotate(-2deg);
}

#profile-name {
    font-size: 2.2rem;
    margin: 0;
    font-weight: 700;
    letter-spacing: 1px;
}

#profile-tagline {
    font-size: 1.15rem;
    margin-top: 10px;
    font-style: italic;
}

.profile-details h2 {
    color: #fff;
    margin-top: 28px;
    margin-bottom: 10px;
    font-size: 1.15rem;
    font-weight: 600;
    letter-spacing: 0.5px;
}

.profile-details p, .profile-details ul {
    color: #fff;
    font-size: 1.07rem;
    margin-bottom: 16px;
    line-height: 1.7;
    word-wrap: break-word;
    overflow-wrap: break-word;
}

.profile-details ul {
    list-style: none;
    padding-left: 0;
    display: flex;
    flex-wrap: wrap;
    gap: 8px 12px;
    margin-bottom: 18px;
    justify-content: flex-start;
    align-items: stretch;
}
.profile-details ul li {
    background: linear-gradient(90deg, #e0c3fc 0%, #8ec5fc 100%);
    border-radius: 18px;
    padding: 8px 16px;
    font-size: 1rem;
    color: #4a3f6b;
    box-shadow: 0 2px 8px rgba(80,80,160,0.09);
    transition: background 0.2s, transform 0.2s;
    font-weight: 500;
    flex: 0 0 auto;
    min-width: fit-content;
    max-width: calc(50% - 6px);
    text-align: center;
    word-break: break-word;
    hyphens: auto;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}
.profile-details ul li:hover {
    background: linear-gradient(90deg, #8ec5fc 0%, #e0c3fc 100%);
    transform: scale(1.04);
    white-space: normal;
    overflow: visible;
    z-index: 10;
    position: relative;
}

/* Adaptive sizing for different content lengths */
.profile-details ul li.short-text {
    flex: 0 0 auto;
    max-width: 200px;
}

.profile-details ul li.medium-text {
    flex: 0 0 auto;
    max-width: calc(50% - 6px);
}

.profile-details ul li.long-text {
    flex: 1 1 100%;
    max-width: 100%;
}

/* Better distribution for mixed content lengths */
@media (min-width: 769px) {
    .profile-details ul li {
        flex: 0 1 auto;
        max-width: calc(33.333% - 8px);
        min-width: 150px;
    }

    .profile-details ul li.long-text {
        flex: 1 1 calc(66.666% - 8px);
        max-width: calc(66.666% - 8px);
    }
}

@media (min-width: 1025px) {
    .profile-details ul li {
        max-width: calc(25% - 9px);
        min-width: 180px;
    }

    .profile-details ul li.long-text {
        flex: 1 1 calc(50% - 9px);
        max-width: calc(50% - 9px);
    }
}

#edit-profile-btn {
    display: block;
    margin: 36px auto 0 auto;
    padding: 12px 32px;
    background: linear-gradient(90deg, #8ec5fc 0%, #e0c3fc 100%);
    color: #fff;
    border: none;
    border-radius: 8px;
    font-weight: 600;
    cursor: pointer;
    box-shadow: 0 2px 8px rgba(80,80,160,0.10);
    transition: background 0.2s, transform 0.2s;
    letter-spacing: 0.5px;
}
#edit-profile-btn:hover {
    background: linear-gradient(90deg, #e0c3fc 0%, #8ec5fc 100%);
    transform: scale(1.04);
}

.modal {
    position: fixed;
    top: 0; left: 0; right: 0; bottom: 0;
    background: rgba(80,80,160,0.18);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
    animation: fadeIn 0.3s;
    padding: 20px;
    box-sizing: border-box;
    overflow-y: auto;
}
.modal.hidden {
    display: none;
}
@keyframes fadeIn {
    0% { opacity: 0; }
    100% { opacity: 1; }
}
.modal-content {
    background: #232323;
    border-radius: 24px;
    box-shadow: 0 12px 40px rgba(255,140,0,0.18);
    padding: 48px 36px 36px 36px;
    position: relative;
    animation: fadeInUp 0.8s cubic-bezier(.39,.575,.565,1.000);
    border: 2px solid #fff;
    z-index: 1;
    transition: color 0.2s;
    max-width: 540px;
    width: 100%;
    max-height: 90vh;
    overflow-y: auto;
    box-sizing: border-box;
}

.close-btn {
    position: absolute;
    top: 16px;
    right: 20px;
    font-size: 24px;
    color: #fff;
    cursor: pointer;
    transition: color 0.2s;
    z-index: 2;
}
.close-btn:hover {
    color: #e0c3fc;
}

/* Ensure close button is always visible and accessible */
@media (max-width: 480px) {
    .close-btn {
        top: 12px;
        right: 16px;
        font-size: 20px;
        background: rgba(0, 0, 0, 0.5);
        border-radius: 50%;
        width: 32px;
        height: 32px;
        display: flex;
        align-items: center;
        justify-content: center;
        line-height: 1;
    }
}
label {
    display: block;
    margin-bottom: 12px;
    color: #4a3f6b;
    font-weight: 500;
}
input[type="text"], textarea {
    width: 100%;
    padding: 10px;
    margin-top: 6px;
    border: 1px solid #8ec5fc;
    border-radius: 6px;
    font-size: 1rem;
    box-sizing: border-box;
    background: #f7f7fa;
    transition: border 0.2s;
}
input[type="text"]:focus, textarea:focus {
    border: 1.5px solid #e0c3fc;
    outline: none;
}
button[type="submit"] {
    background: linear-gradient(90deg, #8ec5fc 0%, #e0c3fc 100%);
    color: #fff;
    border: none;
    border-radius: 8px;
    padding: 12px 28px;
    font-size: 1.05rem;
    font-weight: 600;
    cursor: pointer;
    margin-top: 18px;
    box-shadow: 0 2px 8px rgba(80,80,160,0.10);
    transition: background 0.2s, transform 0.2s;
    letter-spacing: 0.5px;
}
button[type="submit"]:hover {
    background: linear-gradient(90deg, #e0c3fc 0%, #8ec5fc 100%);
    transform: scale(1.04);
}

/* Large Tablet and Small Desktop Styles */
@media (max-width: 1024px) {
    .profile-container {
        max-width: 85%;
        margin: 40px auto;
        padding: 36px 28px;
    }

    .modal-content {
        max-width: 85%;
        margin: 20px;
        padding: 40px 28px;
    }

    .profile-details ul {
        gap: 6px 10px;
    }

    .profile-details ul li {
        font-size: 0.98rem;
        padding: 7px 15px;
        max-width: calc(50% - 5px);
    }
}

/* Tablet and Small Desktop Styles */
@media (max-width: 992px) {
    .profile-container {
        max-width: 90%;
        margin: 32px auto;
        padding: 32px 24px;
    }

    .modal-content {
        max-width: 90%;
        margin: 20px;
        padding: 32px 24px;
    }

    .profile-details ul {
        gap: 8px 12px;
    }

    .profile-details ul li {
        font-size: 0.96rem;
        padding: 6px 14px;
    }
}

/* Tablet Styles */
@media (max-width: 768px) {
    body {
        padding: 8px;
    }

    .profile-container {
        margin: 16px auto;
        padding: 24px 20px;
        max-width: 95%;
    }

    .profile-details ul {
        gap: 6px 8px;
    }

    .profile-details ul li {
        font-size: 0.95rem;
        padding: 6px 14px;
        max-width: calc(50% - 4px);
        min-width: 120px;
    }

    .modal-content {
        max-width: 95%;
        margin: 10px;
        padding: 24px 20px;
    }

    .contact-section {
        margin-top: 24px;
        padding: 16px 0;
    }

    #contact-details {
        max-width: 90%;
        padding: 20px;
        margin: 20px auto;
    }
}

/* Extra Small Mobile Styles */
@media (max-width: 360px) {
    .profile-container {
        margin: 4px auto;
        padding: 12px 8px;
        border-radius: 8px;
    }

    .profile-photo {
        width: 80px;
        height: 80px;
    }

    #profile-name {
        font-size: 1.6rem;
    }

    .profile-details ul {
        gap: 4px 6px;
    }

    .profile-details ul li {
        font-size: 0.85rem;
        padding: 4px 10px;
        border-radius: 12px;
        max-width: 100%;
        flex: 1 1 100%;
        min-width: 80px;
    }

    .modal-content {
        padding: 16px 12px;
        border-radius: 12px;
    }

    #contact-details {
        padding: 12px;
    }
}

/* Mobile Styles */
@media (max-width: 480px) {
    body {
        padding: 4px;
    }

    .profile-container {
        margin: 8px auto;
        padding: 16px 12px;
        max-width: 98%;
        border-radius: 12px;
    }

    .profile-photo {
        width: 100px;
        height: 100px;
        margin-bottom: 12px;
    }

    #profile-name {
        font-size: 1.8rem;
        letter-spacing: 0.5px;
    }

    #profile-tagline {
        font-size: 1rem;
    }

    .profile-meta {
        font-size: 1rem;
    }

    .profile-details h2 {
        font-size: 1.1rem;
        margin-top: 20px;
        margin-bottom: 8px;
    }

    .profile-details p, .profile-details ul {
        font-size: 0.95rem;
        line-height: 1.6;
    }

    .profile-details ul {
        gap: 6px 8px;
    }

    .profile-details ul li {
        font-size: 0.9rem;
        padding: 5px 12px;
        border-radius: 14px;
        max-width: calc(100% - 8px);
        min-width: 100px;
        flex: 1 1 auto;
    }

    /* Mobile Button Optimizations */
    #edit-profile-btn {
        padding: 12px 24px;
        font-size: 1rem;
        margin-top: 24px;
        min-height: 44px;
    }

    /* Touch-friendly button sizes */
    .btn-section-edit,
    .btn-section-add,
    .btn-section-collapse {
        min-width: 36px;
        min-height: 36px;
        padding: 6px;
        font-size: 0.9rem;
    }

    .btn-item-edit,
    .btn-item-remove {
        min-width: 32px;
        min-height: 32px;
        padding: 4px;
        font-size: 0.8rem;
    }

    /* Mobile FAB adjustments */
    .fab-container {
        bottom: 16px;
        right: 16px;
    }

    .fab {
        width: 64px;
        height: 64px;
        font-size: 1.4rem;
    }

    .fab-secondary {
        width: 52px;
        height: 52px;
        font-size: 1.1rem;
    }

    /* Mobile button groups */
    .button-group {
        flex-direction: column;
        gap: 12px;
    }

    .button-group .btn {
        min-height: 44px;
        font-size: 1rem;
    }

    /* Mobile section actions - always visible */
    .section-actions {
        opacity: 1;
    }

    /* Mobile context menu */
    .context-menu {
        position: fixed;
        bottom: 0;
        left: 0;
        right: 0;
        border-radius: 16px 16px 0 0;
        padding: 16px;
        transform: translateY(100%);
    }

    .context-menu.active {
        transform: translateY(0);
    }

    .context-menu-item {
        padding: 12px 16px;
        font-size: 1rem;
        min-height: 44px;
        display: flex;
        align-items: center;
    }

    /* Mobile shortcuts modal */
    .shortcuts-content {
        padding: 20px 16px;
        max-width: 95%;
    }

    .shortcuts-grid {
        grid-template-columns: 1fr;
        gap: 16px;
    }

    /* Mobile contact actions */
    .contact-actions {
        flex-direction: column;
        gap: 8px;
    }

    .contact-actions .btn {
        width: 100%;
        justify-content: center;
    }

    /* Mobile quick modals */
    .quick-add-content,
    .quick-edit-content {
        max-width: 95%;
        padding: 16px;
        margin: 10px;
    }

    .quick-add-actions,
    .quick-edit-actions {
        flex-direction: column;
        gap: 8px;
    }

    .quick-add-actions .btn,
    .quick-edit-actions .btn {
        width: 100%;
        min-height: 44px;
    }

    /* Mobile section headers */
    .section-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 8px;
    }

    .section-header h2 {
        width: 100%;
    }

    .section-actions {
        width: 100%;
        justify-content: flex-end;
    }

    /* Mobile item layout */
    .list-group-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 8px;
    }

    .item-text {
        width: 100%;
        margin-right: 0;
    }

    .item-actions {
        width: 100%;
        justify-content: flex-end;
        opacity: 1;
    }

    /* Mobile advanced controls */
    .advanced-controls {
        padding: 8px;
    }

    .advanced-controls .btn {
        width: 100%;
        margin: 2px 0;
    }

    .modal-content {
        padding: 20px 16px;
        max-width: 98%;
        margin: 5px;
        border-radius: 16px;
    }

    .modal-content h2 {
        font-size: 1.3rem;
        margin-bottom: 16px;
    }

    .modal-content textarea {
        font-size: 0.95rem;
        padding: 8px;
    }

    .contact-section {
        margin-top: 20px;
        padding: 12px 0;
    }

    #contact-details {
        max-width: 95%;
        padding: 16px;
        margin: 16px auto;
        border-radius: 12px;
    }

    #contact-details h2 {
        font-size: 1.2rem;
        margin-bottom: 12px;
    }

    #contact-details p {
        font-size: 0.95rem;
        margin-bottom: 8px;
    }

    .footer {
        margin-top: 24px;
        padding: 16px 0;
    }

    .footer p {
        font-size: 0.85rem;
    }
}
