{"name": "fortune500-admin-panel", "version": "1.0.0", "description": "Enterprise-grade admin panel with Fortune 500 security standards and dating profile management", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "jest", "test:security": "npm audit && snyk test", "lint": "eslint .", "build": "webpack --mode production", "migrate": "node scripts/migrate.js", "seed": "node scripts/seed.js", "init-profile": "node scripts/init-dating-profile.js", "setup": "npm run init-profile"}, "dependencies": {"express": "^4.18.2", "express-rate-limit": "^7.1.5", "helmet": "^7.1.0", "cors": "^2.8.5", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.2", "speakeasy": "^2.0.0", "qrcode": "^1.5.3", "mongoose": "^8.0.3", "joi": "^17.11.0", "express-validator": "^7.0.1", "express-session": "^1.17.3", "connect-mongo": "^5.1.0", "winston": "^3.11.0", "morgan": "^1.10.0", "compression": "^1.7.4", "express-slow-down": "^2.0.1", "express-brute": "^1.0.1", "express-brute-mongo": "^1.0.0", "csurf": "^1.11.0", "cookie-parser": "^1.4.6", "multer": "^1.4.5-lts.1", "sharp": "^0.33.1", "nodemailer": "^6.9.7", "crypto": "^1.0.1", "dotenv": "^16.3.1", "express-mongo-sanitize": "^2.2.0", "xss": "^1.0.14", "hpp": "^0.2.3"}, "devDependencies": {"nodemon": "^3.0.2", "jest": "^29.7.0", "supertest": "^6.3.3", "eslint": "^8.55.0", "webpack": "^5.89.0", "webpack-cli": "^5.1.4", "snyk": "^1.1248.0"}, "engines": {"node": ">=18.0.0"}, "keywords": ["admin-panel", "enterprise", "security", "fortune500", "authentication", "rbac"], "author": "Enterprise Development Team", "license": "PROPRIETARY"}