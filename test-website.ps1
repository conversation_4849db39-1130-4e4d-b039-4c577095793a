#!/usr/bin/env pwsh

<#
.SYNOPSIS
    Comprehensive testing script for Blog Website
.DESCRIPTION
    Automated testing suite that includes unit tests, integration tests, 
    security tests, and performance tests for the blog website.
.PARAMETER TestType
    Type of tests to run: All, Unit, Integration, Security, Performance, E2E
.PARAMETER Environment
    Environment to test: Development, Staging, Production
.PARAMETER Verbose
    Enable verbose output
.PARAMETER GenerateReport
    Generate HTML test report
.EXAMPLE
    .\test-website.ps1 -TestType All -Environment Development -Verbose
.EXAMPLE
    .\test-website.ps1 -TestType Security -GenerateReport
#>

param(
    [Parameter(Mandatory=$false)]
    [ValidateSet("All", "Unit", "Integration", "Security", "Performance", "E2E")]
    [string]$TestType = "All",
    
    [Parameter(Mandatory=$false)]
    [ValidateSet("Development", "Staging", "Production")]
    [string]$Environment = "Development",
    
    [Parameter(Mandatory=$false)]
    [switch]$Verbose,
    
    [Parameter(Mandatory=$false)]
    [switch]$GenerateReport,
    
    [Parameter(Mandatory=$false)]
    [string]$ReportPath = "test-results"
)

# Script configuration
$ErrorActionPreference = "Stop"
$ProgressPreference = "SilentlyContinue"

# Global variables
$script:TestResults = @()
$script:StartTime = Get-Date
$script:ProjectRoot = Split-Path -Parent $MyInvocation.MyCommand.Path
$script:AdminSystemPath = Join-Path $ProjectRoot "admin-system"
$script:DatingProfilePath = Join-Path $ProjectRoot "Dating-Resume-Profile"

# Colors for output
$Colors = @{
    Success = "Green"
    Error = "Red"
    Warning = "Yellow"
    Info = "Cyan"
    Header = "Magenta"
}

function Write-ColorOutput {
    param(
        [string]$Message,
        [string]$Color = "White"
    )
    Write-Host $Message -ForegroundColor $Colors[$Color]
}

function Write-TestHeader {
    param([string]$Title)
    Write-Host "`n" + "="*60 -ForegroundColor $Colors.Header
    Write-Host " $Title" -ForegroundColor $Colors.Header
    Write-Host "="*60 -ForegroundColor $Colors.Header
}

function Add-TestResult {
    param(
        [string]$TestName,
        [string]$Status,
        [string]$Message = "",
        [double]$Duration = 0,
        [string]$Category = "General"
    )
    
    $script:TestResults += [PSCustomObject]@{
        TestName = $TestName
        Status = $Status
        Message = $Message
        Duration = $Duration
        Category = $Category
        Timestamp = Get-Date
    }
    
    $color = switch ($Status) {
        "PASS" { "Success" }
        "FAIL" { "Error" }
        "SKIP" { "Warning" }
        default { "Info" }
    }
    
    $durationText = if ($Duration -gt 0) { " ({0:F2}s)" -f $Duration } else { "" }
    Write-ColorOutput "  [$Status] $TestName$durationText - $Message" $color
}

function Test-Prerequisites {
    Write-TestHeader "Checking Prerequisites"
    
    # Check Node.js
    try {
        $nodeVersion = node --version 2>$null
        if ($nodeVersion) {
            Add-TestResult "Node.js Installation" "PASS" "Version: $nodeVersion" 0 "Prerequisites"
        } else {
            Add-TestResult "Node.js Installation" "FAIL" "Node.js not found" 0 "Prerequisites"
            return $false
        }
    } catch {
        Add-TestResult "Node.js Installation" "FAIL" "Node.js not found" 0 "Prerequisites"
        return $false
    }
    
    # Check npm
    try {
        $npmVersion = npm --version 2>$null
        if ($npmVersion) {
            Add-TestResult "npm Installation" "PASS" "Version: $npmVersion" 0 "Prerequisites"
        } else {
            Add-TestResult "npm Installation" "FAIL" "npm not found" 0 "Prerequisites"
            return $false
        }
    } catch {
        Add-TestResult "npm Installation" "FAIL" "npm not found" 0 "Prerequisites"
        return $false
    }
    
    # Check project structure
    $requiredPaths = @(
        $AdminSystemPath,
        (Join-Path $AdminSystemPath "package.json"),
        (Join-Path $AdminSystemPath "server.js"),
        $DatingProfilePath
    )
    
    foreach ($path in $requiredPaths) {
        if (Test-Path $path) {
            Add-TestResult "Project Structure" "PASS" "Found: $(Split-Path -Leaf $path)" 0 "Prerequisites"
        } else {
            Add-TestResult "Project Structure" "FAIL" "Missing: $path" 0 "Prerequisites"
            return $false
        }
    }
    
    return $true
}

function Install-Dependencies {
    Write-TestHeader "Installing Dependencies"
    
    Push-Location $AdminSystemPath
    try {
        $startTime = Get-Date
        $output = npm install 2>&1
        $duration = (Get-Date) - $startTime
        
        if ($LASTEXITCODE -eq 0) {
            Add-TestResult "npm install" "PASS" "Dependencies installed successfully" $duration.TotalSeconds "Setup"
        } else {
            Add-TestResult "npm install" "FAIL" "Failed to install dependencies: $output" $duration.TotalSeconds "Setup"
            return $false
        }
    } catch {
        Add-TestResult "npm install" "FAIL" "Exception: $($_.Exception.Message)" 0 "Setup"
        return $false
    } finally {
        Pop-Location
    }
    
    return $true
}

function Test-UnitTests {
    Write-TestHeader "Running Unit Tests"
    
    Push-Location $AdminSystemPath
    try {
        $startTime = Get-Date
        $output = npm test 2>&1
        $duration = (Get-Date) - $startTime
        
        if ($LASTEXITCODE -eq 0) {
            Add-TestResult "Jest Unit Tests" "PASS" "All unit tests passed" $duration.TotalSeconds "Unit"
        } else {
            Add-TestResult "Jest Unit Tests" "FAIL" "Unit tests failed: $output" $duration.TotalSeconds "Unit"
        }
    } catch {
        Add-TestResult "Jest Unit Tests" "FAIL" "Exception: $($_.Exception.Message)" 0 "Unit"
    } finally {
        Pop-Location
    }
}

function Test-SecurityTests {
    Write-TestHeader "Running Security Tests"
    
    Push-Location $AdminSystemPath
    try {
        # Run security audit
        $startTime = Get-Date
        $auditOutput = npm audit --audit-level=moderate 2>&1
        $auditDuration = (Get-Date) - $startTime
        
        if ($LASTEXITCODE -eq 0) {
            Add-TestResult "npm audit" "PASS" "No security vulnerabilities found" $auditDuration.TotalSeconds "Security"
        } else {
            Add-TestResult "npm audit" "FAIL" "Security vulnerabilities detected" $auditDuration.TotalSeconds "Security"
        }
        
        # Run security-specific tests
        if (Test-Path "npm run test:security") {
            $startTime = Get-Date
            $securityOutput = npm run test:security 2>&1
            $securityDuration = (Get-Date) - $startTime
            
            if ($LASTEXITCODE -eq 0) {
                Add-TestResult "Security Test Suite" "PASS" "All security tests passed" $securityDuration.TotalSeconds "Security"
            } else {
                Add-TestResult "Security Test Suite" "FAIL" "Security tests failed" $securityDuration.TotalSeconds "Security"
            }
        } else {
            Add-TestResult "Security Test Suite" "SKIP" "No security test script found" 0 "Security"
        }
        
    } catch {
        Add-TestResult "Security Tests" "FAIL" "Exception: $($_.Exception.Message)" 0 "Security"
    } finally {
        Pop-Location
    }
}

function Test-IntegrationTests {
    Write-TestHeader "Running Integration Tests"
    
    # Start the server for integration testing
    $serverProcess = $null
    try {
        Push-Location $AdminSystemPath
        
        # Start server in background
        Write-ColorOutput "Starting server for integration tests..." "Info"
        $serverProcess = Start-Process -FilePath "node" -ArgumentList "server.js" -PassThru -WindowStyle Hidden
        Start-Sleep -Seconds 5
        
        # Test server health
        $startTime = Get-Date
        try {
            $response = Invoke-RestMethod -Uri "http://localhost:3000/health" -Method GET -TimeoutSec 10
            $duration = (Get-Date) - $startTime
            
            if ($response.status -eq "OK") {
                Add-TestResult "Server Health Check" "PASS" "Server is responding" $duration.TotalSeconds "Integration"
            } else {
                Add-TestResult "Server Health Check" "FAIL" "Server health check failed" $duration.TotalSeconds "Integration"
            }
        } catch {
            Add-TestResult "Server Health Check" "FAIL" "Cannot connect to server: $($_.Exception.Message)" 0 "Integration"
        }
        
        # Test API endpoints
        Test-ApiEndpoints
        
    } finally {
        if ($serverProcess) {
            Write-ColorOutput "Stopping test server..." "Info"
            Stop-Process -Id $serverProcess.Id -Force -ErrorAction SilentlyContinue
        }
        Pop-Location
    }
}

function Test-ApiEndpoints {
    $endpoints = @(
        @{ Url = "http://localhost:3000/health"; Method = "GET"; ExpectedStatus = 200; Name = "Health Endpoint" },
        @{ Url = "http://localhost:3000/api/csrf-token"; Method = "GET"; ExpectedStatus = 200; Name = "CSRF Token Endpoint" },
        @{ Url = "http://localhost:3000/api/public"; Method = "GET"; ExpectedStatus = 200; Name = "Public API Info" },
        @{ Url = "http://localhost:3000/"; Method = "GET"; ExpectedStatus = 200; Name = "Main Website" }
    )
    
    foreach ($endpoint in $endpoints) {
        $startTime = Get-Date
        try {
            $response = Invoke-WebRequest -Uri $endpoint.Url -Method $endpoint.Method -TimeoutSec 10 -UseBasicParsing
            $duration = (Get-Date) - $startTime
            
            if ($response.StatusCode -eq $endpoint.ExpectedStatus) {
                Add-TestResult $endpoint.Name "PASS" "Status: $($response.StatusCode)" $duration.TotalSeconds "Integration"
            } else {
                Add-TestResult $endpoint.Name "FAIL" "Expected: $($endpoint.ExpectedStatus), Got: $($response.StatusCode)" $duration.TotalSeconds "Integration"
            }
        } catch {
            $duration = (Get-Date) - $startTime
            Add-TestResult $endpoint.Name "FAIL" "Request failed: $($_.Exception.Message)" $duration.TotalSeconds "Integration"
        }
    }
}

function Test-PerformanceTests {
    Write-TestHeader "Running Performance Tests"
    
    # Test file sizes
    $jsFiles = Get-ChildItem -Path $AdminSystemPath -Recurse -Include "*.js" | Where-Object { $_.Length -gt 100KB }
    if ($jsFiles.Count -gt 0) {
        Add-TestResult "JavaScript File Sizes" "FAIL" "Found $($jsFiles.Count) large JS files (>100KB)" 0 "Performance"
    } else {
        Add-TestResult "JavaScript File Sizes" "PASS" "All JS files are reasonably sized" 0 "Performance"
    }
    
    # Test CSS files
    $cssFiles = Get-ChildItem -Path $AdminSystemPath -Recurse -Include "*.css" | Where-Object { $_.Length -gt 50KB }
    if ($cssFiles.Count -gt 0) {
        Add-TestResult "CSS File Sizes" "FAIL" "Found $($cssFiles.Count) large CSS files (>50KB)" 0 "Performance"
    } else {
        Add-TestResult "CSS File Sizes" "PASS" "All CSS files are reasonably sized" 0 "Performance"
    }
    
    # Test for common performance issues
    $performanceIssues = @()
    
    # Check for console.log statements in production code
    $consoleLogFiles = Get-ChildItem -Path $AdminSystemPath -Recurse -Include "*.js" | 
        Select-String -Pattern "console\.log" | 
        Group-Object Filename | 
        Select-Object Name
    
    if ($consoleLogFiles.Count -gt 0) {
        $performanceIssues += "Found console.log statements in $($consoleLogFiles.Count) files"
    }
    
    if ($performanceIssues.Count -gt 0) {
        Add-TestResult "Performance Issues" "FAIL" ($performanceIssues -join "; ") 0 "Performance"
    } else {
        Add-TestResult "Performance Issues" "PASS" "No obvious performance issues found" 0 "Performance"
    }
}

function Test-E2ETests {
    Write-TestHeader "Running End-to-End Tests"
    
    # Basic E2E tests using PowerShell web requests
    $serverProcess = $null
    try {
        Push-Location $AdminSystemPath
        
        # Start server
        Write-ColorOutput "Starting server for E2E tests..." "Info"
        $serverProcess = Start-Process -FilePath "node" -ArgumentList "server.js" -PassThru -WindowStyle Hidden
        Start-Sleep -Seconds 5
        
        # Test complete user workflow
        Test-UserWorkflow
        
    } finally {
        if ($serverProcess) {
            Stop-Process -Id $serverProcess.Id -Force -ErrorAction SilentlyContinue
        }
        Pop-Location
    }
}

function Test-UserWorkflow {
    # Test accessing main website
    $startTime = Get-Date
    try {
        $response = Invoke-WebRequest -Uri "http://localhost:3000/" -UseBasicParsing -TimeoutSec 10
        $duration = (Get-Date) - $startTime
        
        if ($response.StatusCode -eq 200 -and $response.Content -match "Blog Website") {
            Add-TestResult "Main Website Access" "PASS" "Website loads correctly" $duration.TotalSeconds "E2E"
        } else {
            Add-TestResult "Main Website Access" "FAIL" "Website content not as expected" $duration.TotalSeconds "E2E"
        }
    } catch {
        $duration = (Get-Date) - $startTime
        Add-TestResult "Main Website Access" "FAIL" "Cannot access website: $($_.Exception.Message)" $duration.TotalSeconds "E2E"
    }
    
    # Test admin panel access (should redirect to login)
    $startTime = Get-Date
    try {
        $response = Invoke-WebRequest -Uri "http://localhost:3000/admin" -UseBasicParsing -TimeoutSec 10 -MaximumRedirection 0 -ErrorAction SilentlyContinue
        $duration = (Get-Date) - $startTime
        
        # Should get a redirect or serve the admin page
        if ($response.StatusCode -eq 200 -or $response.StatusCode -eq 302) {
            Add-TestResult "Admin Panel Access" "PASS" "Admin panel accessible" $duration.TotalSeconds "E2E"
        } else {
            Add-TestResult "Admin Panel Access" "FAIL" "Unexpected response: $($response.StatusCode)" $duration.TotalSeconds "E2E"
        }
    } catch {
        $duration = (Get-Date) - $startTime
        Add-TestResult "Admin Panel Access" "FAIL" "Cannot access admin panel: $($_.Exception.Message)" $duration.TotalSeconds "E2E"
    }
}

function Generate-TestReport {
    if (-not $GenerateReport) { return }
    
    Write-TestHeader "Generating Test Report"
    
    $reportDir = Join-Path $ProjectRoot $ReportPath
    if (-not (Test-Path $reportDir)) {
        New-Item -ItemType Directory -Path $reportDir -Force | Out-Null
    }
    
    $reportFile = Join-Path $reportDir "test-report-$(Get-Date -Format 'yyyyMMdd-HHmmss').html"
    
    $totalTests = $script:TestResults.Count
    $passedTests = ($script:TestResults | Where-Object { $_.Status -eq "PASS" }).Count
    $failedTests = ($script:TestResults | Where-Object { $_.Status -eq "FAIL" }).Count
    $skippedTests = ($script:TestResults | Where-Object { $_.Status -eq "SKIP" }).Count
    $totalDuration = ($script:TestResults | Measure-Object -Property Duration -Sum).Sum
    
    $html = @"
<!DOCTYPE html>
<html>
<head>
    <title>Blog Website Test Report</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .header { background: #f0f0f0; padding: 20px; border-radius: 5px; }
        .summary { display: flex; gap: 20px; margin: 20px 0; }
        .metric { background: #e9ecef; padding: 15px; border-radius: 5px; text-align: center; }
        .pass { color: green; }
        .fail { color: red; }
        .skip { color: orange; }
        table { width: 100%; border-collapse: collapse; margin: 20px 0; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; }
        .status-PASS { background-color: #d4edda; }
        .status-FAIL { background-color: #f8d7da; }
        .status-SKIP { background-color: #fff3cd; }
    </style>
</head>
<body>
    <div class="header">
        <h1>Blog Website Test Report</h1>
        <p>Generated: $(Get-Date)</p>
        <p>Environment: $Environment</p>
        <p>Test Type: $TestType</p>
    </div>
    
    <div class="summary">
        <div class="metric">
            <h3>Total Tests</h3>
            <div>$totalTests</div>
        </div>
        <div class="metric">
            <h3 class="pass">Passed</h3>
            <div>$passedTests</div>
        </div>
        <div class="metric">
            <h3 class="fail">Failed</h3>
            <div>$failedTests</div>
        </div>
        <div class="metric">
            <h3 class="skip">Skipped</h3>
            <div>$skippedTests</div>
        </div>
        <div class="metric">
            <h3>Duration</h3>
            <div>$("{0:F2}s" -f $totalDuration)</div>
        </div>
    </div>
    
    <table>
        <thead>
            <tr>
                <th>Test Name</th>
                <th>Category</th>
                <th>Status</th>
                <th>Message</th>
                <th>Duration</th>
                <th>Timestamp</th>
            </tr>
        </thead>
        <tbody>
"@
    
    foreach ($result in $script:TestResults) {
        $html += @"
            <tr class="status-$($result.Status)">
                <td>$($result.TestName)</td>
                <td>$($result.Category)</td>
                <td>$($result.Status)</td>
                <td>$($result.Message)</td>
                <td>$("{0:F2}s" -f $result.Duration)</td>
                <td>$($result.Timestamp.ToString("yyyy-MM-dd HH:mm:ss"))</td>
            </tr>
"@
    }
    
    $html += @"
        </tbody>
    </table>
</body>
</html>
"@
    
    $html | Out-File -FilePath $reportFile -Encoding UTF8
    Add-TestResult "Test Report Generation" "PASS" "Report saved to: $reportFile" 0 "Reporting"
}

function Show-TestSummary {
    Write-TestHeader "Test Summary"
    
    $totalTests = $script:TestResults.Count
    $passedTests = ($script:TestResults | Where-Object { $_.Status -eq "PASS" }).Count
    $failedTests = ($script:TestResults | Where-Object { $_.Status -eq "FAIL" }).Count
    $skippedTests = ($script:TestResults | Where-Object { $_.Status -eq "SKIP" }).Count
    $totalDuration = (Get-Date) - $script:StartTime
    
    Write-ColorOutput "Total Tests: $totalTests" "Info"
    Write-ColorOutput "Passed: $passedTests" "Success"
    Write-ColorOutput "Failed: $failedTests" "Error"
    Write-ColorOutput "Skipped: $skippedTests" "Warning"
    Write-ColorOutput "Total Duration: $("{0:F2}" -f $totalDuration.TotalSeconds) seconds" "Info"
    
    if ($failedTests -gt 0) {
        Write-ColorOutput "`nFailed Tests:" "Error"
        $script:TestResults | Where-Object { $_.Status -eq "FAIL" } | ForEach-Object {
            Write-ColorOutput "  - $($_.TestName): $($_.Message)" "Error"
        }
    }
    
    $successRate = if ($totalTests -gt 0) { ($passedTests / $totalTests) * 100 } else { 0 }
    Write-ColorOutput "`nSuccess Rate: $("{0:F1}" -f $successRate)%" $(if ($successRate -ge 80) { "Success" } else { "Error" })
}

# Main execution
function Main {
    Write-ColorOutput "Blog Website Testing Suite" "Header"
    Write-ColorOutput "Test Type: $TestType" "Info"
    Write-ColorOutput "Environment: $Environment" "Info"
    Write-ColorOutput "Started: $(Get-Date)" "Info"
    
    # Check prerequisites
    if (-not (Test-Prerequisites)) {
        Write-ColorOutput "Prerequisites check failed. Exiting." "Error"
        exit 1
    }
    
    # Install dependencies
    if (-not (Install-Dependencies)) {
        Write-ColorOutput "Dependency installation failed. Exiting." "Error"
        exit 1
    }
    
    # Run tests based on type
    switch ($TestType) {
        "All" {
            Test-UnitTests
            Test-SecurityTests
            Test-IntegrationTests
            Test-PerformanceTests
            Test-E2ETests
        }
        "Unit" { Test-UnitTests }
        "Integration" { Test-IntegrationTests }
        "Security" { Test-SecurityTests }
        "Performance" { Test-PerformanceTests }
        "E2E" { Test-E2ETests }
    }
    
    # Generate report if requested
    Generate-TestReport
    
    # Show summary
    Show-TestSummary
    
    # Exit with appropriate code
    $failedTests = ($script:TestResults | Where-Object { $_.Status -eq "FAIL" }).Count
    exit $(if ($failedTests -gt 0) { 1 } else { 0 })
}

# Run the main function
Main
