const winston = require('winston');
const path = require('path');
const fs = require('fs');

/**
 * Enterprise Logging System
 * Implements structured logging with security event correlation
 */

// Ensure logs directory exists
const logsDir = path.join(__dirname, '../logs');
if (!fs.existsSync(logsDir)) {
  fs.mkdirSync(logsDir, { recursive: true });
}

// Custom log format
const logFormat = winston.format.combine(
  winston.format.timestamp({
    format: 'YYYY-MM-DD HH:mm:ss.SSS'
  }),
  winston.format.errors({ stack: true }),
  winston.format.json(),
  winston.format.printf(({ timestamp, level, message, ...meta }) => {
    return JSON.stringify({
      timestamp,
      level: level.toUpperCase(),
      message,
      ...meta
    });
  })
);

// Security-focused log format
const securityLogFormat = winston.format.combine(
  winston.format.timestamp({
    format: 'YYYY-MM-DD HH:mm:ss.SSS'
  }),
  winston.format.json(),
  winston.format.printf(({ timestamp, level, message, ...meta }) => {
    return JSON.stringify({
      timestamp,
      level: level.toUpperCase(),
      event_type: 'security',
      message,
      severity: meta.severity || 'medium',
      source: 'admin-panel',
      ...meta
    });
  })
);

// Create logger instance
const logger = winston.createLogger({
  level: process.env.LOG_LEVEL || 'info',
  format: logFormat,
  defaultMeta: {
    service: 'admin-panel',
    version: process.env.APP_VERSION || '1.0.0',
    environment: process.env.NODE_ENV || 'development'
  },
  transports: [
    // Error log file
    new winston.transports.File({
      filename: path.join(logsDir, 'error.log'),
      level: 'error',
      maxsize: 50 * 1024 * 1024, // 50MB
      maxFiles: 10,
      tailable: true
    }),

    // Combined log file
    new winston.transports.File({
      filename: path.join(logsDir, 'combined.log'),
      maxsize: 100 * 1024 * 1024, // 100MB
      maxFiles: 20,
      tailable: true
    }),

    // Security events log
    new winston.transports.File({
      filename: path.join(logsDir, 'security.log'),
      level: 'warn',
      format: securityLogFormat,
      maxsize: 50 * 1024 * 1024, // 50MB
      maxFiles: 30, // Keep longer for security analysis
      tailable: true
    }),

    // Audit log (all admin actions)
    new winston.transports.File({
      filename: path.join(logsDir, 'audit.log'),
      format: winston.format.combine(
        winston.format.timestamp(),
        winston.format.json(),
        winston.format.printf(({ timestamp, level, message, ...meta }) => {
          if (meta.userId || meta.action || meta.resource) {
            return JSON.stringify({
              timestamp,
              level: level.toUpperCase(),
              event_type: 'audit',
              message,
              ...meta
            });
          }
          return null; // Don't log non-audit events to audit log
        })
      ),
      maxsize: 100 * 1024 * 1024, // 100MB
      maxFiles: 50, // Keep for compliance
      tailable: true
    })
  ],

  // Handle exceptions and rejections
  exceptionHandlers: [
    new winston.transports.File({
      filename: path.join(logsDir, 'exceptions.log'),
      maxsize: 50 * 1024 * 1024,
      maxFiles: 5
    })
  ],

  rejectionHandlers: [
    new winston.transports.File({
      filename: path.join(logsDir, 'rejections.log'),
      maxsize: 50 * 1024 * 1024,
      maxFiles: 5
    })
  ]
});

// Add console transport for development
if (process.env.NODE_ENV !== 'production') {
  logger.add(new winston.transports.Console({
    format: winston.format.combine(
      winston.format.colorize(),
      winston.format.simple(),
      winston.format.printf(({ timestamp, level, message, ...meta }) => {
        const metaStr = Object.keys(meta).length ? JSON.stringify(meta, null, 2) : '';
        return `${timestamp} [${level}]: ${message} ${metaStr}`;
      })
    )
  }));
}

// Security event logger
const securityLogger = {
  loginAttempt: (email, ip, success, reason = null) => {
    logger.warn('Login attempt', {
      event: 'login_attempt',
      email,
      ip,
      success,
      reason,
      severity: success ? 'low' : 'medium'
    });
  },

  loginSuccess: (userId, username, ip, mfaUsed = false) => {
    logger.info('Successful login', {
      event: 'login_success',
      userId,
      username,
      ip,
      mfaUsed,
      severity: 'low'
    });
  },

  loginFailure: (email, ip, reason) => {
    logger.warn('Failed login', {
      event: 'login_failure',
      email,
      ip,
      reason,
      severity: 'medium'
    });
  },

  suspiciousActivity: (ip, activity, details = {}) => {
    logger.warn('Suspicious activity detected', {
      event: 'suspicious_activity',
      ip,
      activity,
      details,
      severity: 'high'
    });
  },

  securityViolation: (userId, violation, details = {}) => {
    logger.error('Security violation', {
      event: 'security_violation',
      userId,
      violation,
      details,
      severity: 'critical'
    });
  },

  rateLimitExceeded: (ip, endpoint, limit) => {
    logger.warn('Rate limit exceeded', {
      event: 'rate_limit_exceeded',
      ip,
      endpoint,
      limit,
      severity: 'medium'
    });
  },

  accountLocked: (userId, reason, duration) => {
    logger.warn('Account locked', {
      event: 'account_locked',
      userId,
      reason,
      duration,
      severity: 'high'
    });
  },

  privilegeEscalation: (userId, fromRole, toRole, grantedBy) => {
    logger.warn('Privilege escalation', {
      event: 'privilege_escalation',
      userId,
      fromRole,
      toRole,
      grantedBy,
      severity: 'high'
    });
  },

  dataAccess: (userId, resource, action, success) => {
    logger.info('Data access', {
      event: 'data_access',
      userId,
      resource,
      action,
      success,
      severity: success ? 'low' : 'medium'
    });
  },

  configurationChange: (userId, setting, oldValue, newValue) => {
    logger.warn('Configuration change', {
      event: 'configuration_change',
      userId,
      setting,
      oldValue,
      newValue,
      severity: 'high'
    });
  }
};

// Audit event logger
const auditLogger = {
  userAction: (userId, username, action, resource, details = {}) => {
    logger.info('User action', {
      event: 'user_action',
      userId,
      username,
      action,
      resource,
      details,
      timestamp: new Date().toISOString()
    });
  },

  adminAction: (userId, username, action, target, changes = {}) => {
    logger.info('Admin action', {
      event: 'admin_action',
      userId,
      username,
      action,
      target,
      changes,
      timestamp: new Date().toISOString()
    });
  },

  systemEvent: (event, details = {}) => {
    logger.info('System event', {
      event: 'system_event',
      systemEvent: event,
      details,
      timestamp: new Date().toISOString()
    });
  }
};

// Performance logger
const performanceLogger = {
  slowQuery: (query, duration, collection) => {
    logger.warn('Slow database query', {
      event: 'slow_query',
      query,
      duration,
      collection,
      threshold: '1000ms'
    });
  },

  slowRequest: (method, url, duration, statusCode) => {
    logger.warn('Slow HTTP request', {
      event: 'slow_request',
      method,
      url,
      duration,
      statusCode,
      threshold: '5000ms'
    });
  },

  memoryUsage: (usage) => {
    logger.info('Memory usage', {
      event: 'memory_usage',
      ...usage
    });
  }
};

// Error correlation and analysis
const errorAnalyzer = {
  correlateErrors: (errors) => {
    // Group errors by type, time window, and user
    const correlations = {};
    
    errors.forEach(error => {
      const key = `${error.type}_${error.userId || 'anonymous'}`;
      if (!correlations[key]) {
        correlations[key] = [];
      }
      correlations[key].push(error);
    });

    // Identify patterns
    Object.keys(correlations).forEach(key => {
      const errorGroup = correlations[key];
      if (errorGroup.length > 5) { // Threshold for pattern detection
        logger.warn('Error pattern detected', {
          event: 'error_pattern',
          pattern: key,
          count: errorGroup.length,
          timespan: '1h',
          severity: 'high'
        });
      }
    });
  }
};

// Log cleanup utility
const logCleanup = {
  cleanOldLogs: () => {
    const retentionDays = parseInt(process.env.LOG_RETENTION_DAYS) || 90;
    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - retentionDays);

    // This would typically be handled by log rotation
    logger.info('Log cleanup initiated', {
      event: 'log_cleanup',
      retentionDays,
      cutoffDate: cutoffDate.toISOString()
    });
  }
};

// Export logger and utilities
module.exports = {
  logger,
  securityLogger,
  auditLogger,
  performanceLogger,
  errorAnalyzer,
  logCleanup
};
