const mongoose = require('mongoose');
const bcrypt = require('bcryptjs');
const speakeasy = require('speakeasy');
const { securityUtils } = require('../config/security');

/**
 * User Schema with Enterprise Security Features
 * Implements RBAC, MFA, and comprehensive audit trails
 */

const userSchema = new mongoose.Schema({
  // Basic Information
  username: {
    type: String,
    required: true,
    unique: true,
    trim: true,
    minlength: 3,
    maxlength: 50,
    match: /^[a-zA-Z0-9_-]+$/
  },
  email: {
    type: String,
    required: true,
    unique: true,
    trim: true,
    lowercase: true,
    match: /^[^\s@]+@[^\s@]+\.[^\s@]+$/
  },
  password: {
    type: String,
    required: true,
    minlength: 12
  },
  firstName: {
    type: String,
    required: true,
    trim: true,
    maxlength: 50
  },
  lastName: {
    type: String,
    required: true,
    trim: true,
    maxlength: 50
  },

  // Role-Based Access Control
  role: {
    type: String,
    enum: ['super_admin', 'admin', 'editor', 'viewer'],
    default: 'viewer',
    required: true
  },
  permissions: [{
    resource: {
      type: String,
      required: true
    },
    actions: [{
      type: String,
      enum: ['create', 'read', 'update', 'delete', 'manage']
    }]
  }],
  department: {
    type: String,
    trim: true,
    maxlength: 100
  },

  // Multi-Factor Authentication
  mfa: {
    enabled: {
      type: Boolean,
      default: false
    },
    secret: {
      type: String,
      select: false // Don't include in queries by default
    },
    backupCodes: [{
      code: String,
      used: {
        type: Boolean,
        default: false
      },
      usedAt: Date
    }],
    lastUsed: Date
  },

  // Security & Session Management
  security: {
    lastLogin: Date,
    lastLoginIP: String,
    loginAttempts: {
      type: Number,
      default: 0
    },
    lockUntil: Date,
    passwordChangedAt: {
      type: Date,
      default: Date.now
    },
    passwordHistory: [{
      hash: String,
      createdAt: {
        type: Date,
        default: Date.now
      }
    }],
    activeSessions: [{
      sessionId: String,
      ipAddress: String,
      userAgent: String,
      createdAt: {
        type: Date,
        default: Date.now
      },
      lastActivity: {
        type: Date,
        default: Date.now
      }
    }],
    refreshTokens: [{
      token: String,
      createdAt: {
        type: Date,
        default: Date.now
      },
      expiresAt: Date,
      ipAddress: String,
      userAgent: String
    }]
  },

  // Account Status
  status: {
    type: String,
    enum: ['active', 'inactive', 'suspended', 'pending_verification'],
    default: 'pending_verification'
  },
  emailVerified: {
    type: Boolean,
    default: false
  },
  emailVerificationToken: String,
  emailVerificationExpires: Date,

  // Password Reset
  passwordResetToken: String,
  passwordResetExpires: Date,

  // Profile Information
  profile: {
    avatar: String,
    phone: String,
    timezone: {
      type: String,
      default: 'UTC'
    },
    language: {
      type: String,
      default: 'en'
    },
    preferences: {
      theme: {
        type: String,
        enum: ['light', 'dark', 'auto'],
        default: 'light'
      },
      notifications: {
        email: {
          type: Boolean,
          default: true
        },
        security: {
          type: Boolean,
          default: true
        },
        system: {
          type: Boolean,
          default: true
        }
      }
    }
  },

  // Audit Trail
  audit: {
    createdBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User'
    },
    updatedBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User'
    },
    createdAt: {
      type: Date,
      default: Date.now
    },
    updatedAt: {
      type: Date,
      default: Date.now
    },
    deletedAt: Date,
    deletedBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User'
    }
  }
}, {
  timestamps: true,
  toJSON: {
    transform: function(doc, ret) {
      delete ret.password;
      delete ret.mfa.secret;
      delete ret.security.passwordHistory;
      delete ret.security.refreshTokens;
      delete ret.emailVerificationToken;
      delete ret.passwordResetToken;
      return ret;
    }
  }
});

// Indexes for performance and security
userSchema.index({ email: 1 }, { unique: true });
userSchema.index({ username: 1 }, { unique: true });
userSchema.index({ 'security.lastLogin': -1 });
userSchema.index({ status: 1 });
userSchema.index({ role: 1 });
userSchema.index({ 'audit.createdAt': -1 });

// Virtual for account lock status
userSchema.virtual('isLocked').get(function() {
  return !!(this.security.lockUntil && this.security.lockUntil > Date.now());
});

// Pre-save middleware
userSchema.pre('save', async function(next) {
  // Hash password if modified
  if (this.isModified('password')) {
    this.password = await securityUtils.hashPassword(this.password);
    this.security.passwordChangedAt = new Date();
    
    // Add to password history
    this.security.passwordHistory.push({
      hash: this.password,
      createdAt: new Date()
    });
    
    // Keep only last 5 passwords
    if (this.security.passwordHistory.length > 5) {
      this.security.passwordHistory = this.security.passwordHistory.slice(-5);
    }
  }

  // Update audit trail
  if (this.isModified() && !this.isNew) {
    this.audit.updatedAt = new Date();
  }

  next();
});

// Instance methods
userSchema.methods.comparePassword = async function(candidatePassword) {
  return await securityUtils.verifyPassword(candidatePassword, this.password);
};

userSchema.methods.generateMFASecret = function() {
  const secret = speakeasy.generateSecret({
    name: `Fortune500 Admin (${this.email})`,
    issuer: 'Fortune500 Admin Panel',
    length: 32
  });
  
  this.mfa.secret = secret.base32;
  return secret;
};

userSchema.methods.verifyMFAToken = function(token) {
  if (!this.mfa.enabled || !this.mfa.secret) {
    return false;
  }

  return speakeasy.totp.verify({
    secret: this.mfa.secret,
    encoding: 'base32',
    token: token,
    window: 2
  });
};

userSchema.methods.generateBackupCodes = function() {
  const codes = [];
  for (let i = 0; i < 10; i++) {
    codes.push({
      code: securityUtils.generateSecureToken(8).toUpperCase(),
      used: false
    });
  }
  this.mfa.backupCodes = codes;
  return codes.map(c => c.code);
};

userSchema.methods.useBackupCode = function(code) {
  const backupCode = this.mfa.backupCodes.find(c => c.code === code.toUpperCase() && !c.used);
  if (backupCode) {
    backupCode.used = true;
    backupCode.usedAt = new Date();
    return true;
  }
  return false;
};

userSchema.methods.addSession = function(sessionId, ipAddress, userAgent) {
  this.security.activeSessions.push({
    sessionId,
    ipAddress,
    userAgent,
    createdAt: new Date(),
    lastActivity: new Date()
  });

  // Keep only last 5 sessions
  if (this.security.activeSessions.length > 5) {
    this.security.activeSessions = this.security.activeSessions.slice(-5);
  }
};

userSchema.methods.removeSession = function(sessionId) {
  this.security.activeSessions = this.security.activeSessions.filter(
    session => session.sessionId !== sessionId
  );
};

userSchema.methods.addRefreshToken = function(token, expiresAt, ipAddress, userAgent) {
  this.security.refreshTokens.push({
    token,
    createdAt: new Date(),
    expiresAt,
    ipAddress,
    userAgent
  });

  // Clean expired tokens
  this.security.refreshTokens = this.security.refreshTokens.filter(
    rt => rt.expiresAt > new Date()
  );
};

userSchema.methods.removeRefreshToken = function(token) {
  this.security.refreshTokens = this.security.refreshTokens.filter(
    rt => rt.token !== token
  );
};

userSchema.methods.hasPermission = function(resource, action) {
  // Super admin has all permissions
  if (this.role === 'super_admin') {
    return true;
  }

  // Role-based permissions for dating profile
  if (resource === 'dating_profile') {
    if (this.role === 'admin') {
      return ['create', 'read', 'update', 'delete', 'manage'].includes(action);
    }
    if (this.role === 'editor') {
      return ['read', 'update'].includes(action);
    }
    if (this.role === 'viewer') {
      return action === 'read';
    }
  }

  // Role-based permissions for content
  if (resource === 'content') {
    if (this.role === 'admin') {
      return true; // All permissions
    }
    if (this.role === 'editor') {
      return ['create', 'read', 'update', 'publish'].includes(action);
    }
    if (this.role === 'viewer') {
      return action === 'read';
    }
  }

  // Role-based permissions for users
  if (resource === 'users') {
    if (this.role === 'admin') {
      return ['create', 'read', 'update', 'delete'].includes(action);
    }
    return false; // Only admins can manage users
  }

  // Check specific permissions
  const permission = this.permissions.find(p => p.resource === resource);
  return permission && (permission.actions.includes(action) || permission.actions.includes('manage'));
};

// Static methods
userSchema.statics.findByCredentials = async function(email, password) {
  const user = await this.findOne({ 
    email: email.toLowerCase(),
    status: { $in: ['active', 'pending_verification'] }
  });

  if (!user) {
    throw new Error('Invalid credentials');
  }

  if (user.isLocked) {
    throw new Error('Account temporarily locked');
  }

  const isMatch = await user.comparePassword(password);
  if (!isMatch) {
    // Increment login attempts
    user.security.loginAttempts += 1;
    
    // Lock account after 5 failed attempts
    if (user.security.loginAttempts >= 5) {
      user.security.lockUntil = new Date(Date.now() + 15 * 60 * 1000); // 15 minutes
    }
    
    await user.save();
    throw new Error('Invalid credentials');
  }

  // Reset login attempts on successful login
  user.security.loginAttempts = 0;
  user.security.lockUntil = undefined;
  user.security.lastLogin = new Date();
  
  return user;
};

module.exports = mongoose.model('User', userSchema);
