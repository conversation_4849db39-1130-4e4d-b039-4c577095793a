/**
 * Dating Profile Widget
 * Reusable component for displaying dating profile information
 */
class DatingProfileWidget extends BaseComponent {
  getDefaultOptions() {
    return {
      profileId: 'main-profile',
      mode: 'public', // 'public' or 'admin'
      showEditControls: false,
      showStats: false,
      theme: 'default',
      autoRefresh: false,
      refreshInterval: 30000
    };
  }

  init() {
    super.init();
    
    this.profileData = null;
    this.refreshTimer = null;
    
    this.setupEventListeners();
    this.loadProfile();
    
    if (this.options.autoRefresh) {
      this.startAutoRefresh();
    }
  }

  setupEventListeners() {
    // Edit button click
    const editBtn = this.find('.edit-profile-btn');
    if (editBtn) {
      this.addEventListener(editBtn, 'click', this.handleEditClick.bind(this));
    }

    // View stats button click
    const statsBtn = this.find('.view-stats-btn');
    if (statsBtn) {
      this.addEventListener(statsBtn, 'click', this.handleStatsClick.bind(this));
    }

    // Listen for profile updates
    eventBus.on('profile:updated', this.handleProfileUpdate.bind(this));
  }

  async loadProfile() {
    try {
      this.showLoading();
      
      const endpoint = this.options.mode === 'admin' 
        ? `/dating-profile/${this.options.profileId}`
        : `/dating-profile/public/${this.options.profileId}`;
      
      const response = await apiClient.get(endpoint);
      this.profileData = response.data || response.profile;
      
      this.render();
      this.hideLoading();
      
      eventBus.emit('profile:loaded', this.profileData);
    } catch (error) {
      this.showError('Failed to load profile');
      console.error('Profile loading error:', error);
    }
  }

  render() {
    if (!this.profileData) {
      return;
    }

    const template = this.getTemplate();
    this.element.innerHTML = template;
    
    // Re-setup event listeners after render
    this.setupEventListeners();
    
    // Apply theme
    this.applyTheme();
  }

  getTemplate() {
    const profile = this.profileData;
    const isAdmin = this.options.mode === 'admin';
    
    return `
      <div class="dating-profile-widget ${this.options.theme}">
        <div class="profile-header">
          <div class="profile-image">
            <img src="${profile.photo || '/images/default-avatar.jpg'}" 
                 alt="${profile.name}" 
                 class="profile-photo">
            ${isAdmin && this.options.showEditControls ? '<button class="edit-photo-btn">📷</button>' : ''}
          </div>
          <div class="profile-info">
            <h2 class="profile-name">${this.escapeHtml(profile.name)}</h2>
            <p class="profile-age">${profile.age} years old</p>
            ${profile.location ? `<p class="profile-location">📍 ${this.escapeHtml(profile.location)}</p>` : ''}
            ${this.options.showStats ? this.renderStats() : ''}
          </div>
          ${isAdmin && this.options.showEditControls ? this.renderEditControls() : ''}
        </div>
        
        <div class="profile-content">
          ${profile.bio ? `
            <div class="profile-section">
              <h3>About Me</h3>
              <p class="profile-bio">${this.escapeHtml(profile.bio)}</p>
            </div>
          ` : ''}
          
          ${profile.interests && profile.interests.length > 0 ? `
            <div class="profile-section">
              <h3>Interests</h3>
              <div class="interests-list">
                ${profile.interests.map(interest => 
                  `<span class="interest-tag">${this.escapeHtml(interest)}</span>`
                ).join('')}
              </div>
            </div>
          ` : ''}
          
          ${profile.lookingFor ? `
            <div class="profile-section">
              <h3>Looking For</h3>
              <p class="looking-for">${this.escapeHtml(profile.lookingFor)}</p>
            </div>
          ` : ''}
          
          ${this.renderCustomSections()}
        </div>
        
        <div class="profile-footer">
          ${this.renderFooter()}
        </div>
      </div>
    `;
  }

  renderStats() {
    if (!this.profileData.stats) {
      return '';
    }

    const stats = this.profileData.stats;
    return `
      <div class="profile-stats">
        <span class="stat-item">👁️ ${stats.views || 0} views</span>
        <span class="stat-item">💖 ${stats.likes || 0} likes</span>
        ${stats.lastActive ? `<span class="stat-item">🕒 Active ${this.formatDate(stats.lastActive)}</span>` : ''}
      </div>
    `;
  }

  renderEditControls() {
    return `
      <div class="edit-controls">
        <button class="edit-profile-btn btn btn-primary">✏️ Edit</button>
        ${this.options.showStats ? '<button class="view-stats-btn btn btn-secondary">📊 Stats</button>' : ''}
      </div>
    `;
  }

  renderCustomSections() {
    // Override in subclasses for custom sections
    return '';
  }

  renderFooter() {
    const lastUpdated = this.profileData.lastUpdated || this.profileData.updatedAt;
    return lastUpdated ? `
      <p class="last-updated">Last updated: ${this.formatDate(lastUpdated)}</p>
    ` : '';
  }

  showLoading() {
    this.element.innerHTML = `
      <div class="loading-state">
        <div class="spinner"></div>
        <p>Loading profile...</p>
      </div>
    `;
  }

  hideLoading() {
    const loadingState = this.find('.loading-state');
    if (loadingState) {
      loadingState.remove();
    }
  }

  showError(message) {
    this.element.innerHTML = `
      <div class="error-state">
        <div class="error-icon">⚠️</div>
        <p class="error-message">${this.escapeHtml(message)}</p>
        <button class="retry-btn btn btn-primary">Try Again</button>
      </div>
    `;

    const retryBtn = this.find('.retry-btn');
    if (retryBtn) {
      this.addEventListener(retryBtn, 'click', () => this.loadProfile());
    }
  }

  applyTheme() {
    this.element.className = `dating-profile-widget ${this.options.theme}`;
  }

  handleEditClick() {
    eventBus.emit('profile:edit-requested', {
      profileId: this.options.profileId,
      profileData: this.profileData
    });
  }

  handleStatsClick() {
    eventBus.emit('profile:stats-requested', {
      profileId: this.options.profileId,
      profileData: this.profileData
    });
  }

  handleProfileUpdate(updatedProfile) {
    if (updatedProfile.id === this.options.profileId) {
      this.profileData = updatedProfile;
      this.render();
    }
  }

  startAutoRefresh() {
    this.refreshTimer = setInterval(() => {
      this.loadProfile();
    }, this.options.refreshInterval);
  }

  stopAutoRefresh() {
    if (this.refreshTimer) {
      clearInterval(this.refreshTimer);
      this.refreshTimer = null;
    }
  }

  updateProfile(profileData) {
    this.profileData = { ...this.profileData, ...profileData };
    this.render();
    eventBus.emit('profile:updated', this.profileData);
  }

  escapeHtml(text) {
    const div = document.createElement('div');
    div.textContent = text;
    return div.innerHTML;
  }

  formatDate(dateString) {
    const date = new Date(dateString);
    const now = new Date();
    const diffMs = now - date;
    const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));

    if (diffDays === 0) {
      return 'today';
    } else if (diffDays === 1) {
      return 'yesterday';
    } else if (diffDays < 7) {
      return `${diffDays} days ago`;
    } else {
      return date.toLocaleDateString();
    }
  }

  destroy() {
    this.stopAutoRefresh();
    eventBus.off('profile:updated', this.handleProfileUpdate);
    super.destroy();
  }
}

// Register component
componentManager.register('dating-profile-widget', DatingProfileWidget);

// Export for direct use
window.DatingProfileWidget = DatingProfileWidget;
