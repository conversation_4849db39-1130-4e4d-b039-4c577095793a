const jwt = require('jsonwebtoken');
const { logger } = require('../../utils/logger');

/**
 * Authentication and Authorization Middleware
 * Handles JWT authentication, role-based access, and permissions
 */
class AuthMiddleware {
  constructor(config) {
    this.config = config || {};
    this.jwtSecret = process.env.JWT_SECRET;
    this.jwtIssuer = process.env.JWT_ISSUER || 'blog-admin-system';
    this.jwtAudience = process.env.JWT_AUDIENCE || 'blog-admin-users';
  }

  /**
   * JWT Authentication middleware
   */
  authenticate() {
    return async (req, res, next) => {
      try {
        const authHeader = req.headers.authorization;
        const token = authHeader && authHeader.split(' ')[1]; // Bearer TOKEN

        if (!token) {
          return res.status(401).json({
            error: 'Access denied. No token provided.',
            code: 'NO_TOKEN'
          });
        }

        // Verify JWT token
        const decoded = jwt.verify(token, this.jwtSecret, {
          issuer: this.jwtIssuer,
          audience: this.jwtAudience
        });

        // Get user from database
        const User = require('../../models/User');
        const user = await User.findById(decoded.userId).select('+mfa.secret');
        
        if (!user) {
          return res.status(401).json({
            error: 'Invalid token. User not found.',
            code: 'USER_NOT_FOUND'
          });
        }

        // Check if user account is active
        if (user.status !== 'active') {
          logger.warn('Inactive user attempted access', {
            userId: user._id,
            status: user.status,
            ip: req.ip
          });

          return res.status(403).json({
            error: 'User account is not active.',
            code: 'ACCOUNT_INACTIVE'
          });
        }

        // Check if session is still valid
        const session = user.security.activeSessions.find(s => s.sessionId === decoded.sessionId);
        if (!session) {
          return res.status(401).json({
            error: 'Session not found or expired.',
            code: 'SESSION_INVALID'
          });
        }

        // Update last activity
        session.lastActivity = new Date();
        await user.save();

        // Attach user to request
        req.user = user;
        req.tokenData = decoded;

        // Log successful authentication
        logger.debug('User authenticated successfully', {
          userId: user._id,
          username: user.username,
          ip: req.ip,
          endpoint: req.originalUrl
        });

        next();
      } catch (error) {
        logger.warn('Authentication failed', {
          error: error.message,
          ip: req.ip,
          userAgent: req.get('User-Agent'),
          endpoint: req.originalUrl
        });

        if (error.name === 'TokenExpiredError') {
          return res.status(401).json({
            error: 'Token has expired.',
            code: 'TOKEN_EXPIRED'
          });
        }

        if (error.name === 'JsonWebTokenError') {
          return res.status(401).json({
            error: 'Invalid token.',
            code: 'INVALID_TOKEN'
          });
        }

        return res.status(401).json({
          error: 'Authentication failed.',
          code: 'AUTH_FAILED'
        });
      }
    };
  }

  /**
   * Role-based authorization middleware
   */
  authorize(...roles) {
    return (req, res, next) => {
      if (!req.user) {
        return res.status(401).json({
          error: 'Authentication required.',
          code: 'AUTH_REQUIRED'
        });
      }

      if (!roles.includes(req.user.role)) {
        logger.warn('Authorization failed - insufficient role', {
          userId: req.user._id,
          userRole: req.user.role,
          requiredRoles: roles,
          endpoint: req.originalUrl,
          ip: req.ip
        });

        return res.status(403).json({
          error: 'Insufficient permissions.',
          code: 'INSUFFICIENT_PERMISSIONS',
          required: roles,
          current: req.user.role
        });
      }

      next();
    };
  }

  /**
   * Permission-based authorization middleware
   */
  requirePermission(resource, action) {
    return (req, res, next) => {
      if (!req.user) {
        return res.status(401).json({
          error: 'Authentication required.',
          code: 'AUTH_REQUIRED'
        });
      }

      if (!req.user.hasPermission || !req.user.hasPermission(resource, action)) {
        logger.warn('Authorization failed - insufficient permissions', {
          userId: req.user._id,
          username: req.user.username,
          resource,
          action,
          endpoint: req.originalUrl,
          ip: req.ip
        });

        return res.status(403).json({
          error: `Permission denied. Required: ${action} on ${resource}`,
          code: 'PERMISSION_DENIED',
          required: { resource, action }
        });
      }

      next();
    };
  }

  /**
   * MFA verification middleware
   */
  requireMFA() {
    return (req, res, next) => {
      if (!req.user) {
        return res.status(401).json({
          error: 'Authentication required.',
          code: 'AUTH_REQUIRED'
        });
      }

      // Check if MFA is enabled for user
      if (!req.user.mfa.enabled) {
        return next(); // MFA not required for this user
      }

      // Check if MFA was verified in this session
      if (!req.tokenData.mfaVerified) {
        return res.status(403).json({
          error: 'Multi-factor authentication required.',
          code: 'MFA_REQUIRED'
        });
      }

      next();
    };
  }

  /**
   * Admin panel access middleware
   */
  requireAdminAccess() {
    return (req, res, next) => {
      if (!req.user) {
        return res.status(401).json({
          error: 'Authentication required for admin panel access',
          code: 'ADMIN_AUTH_REQUIRED',
          redirectTo: '/admin/login'
        });
      }

      if (!['super_admin', 'admin', 'editor'].includes(req.user.role)) {
        logger.warn('Admin panel access denied', {
          userId: req.user._id,
          userRole: req.user.role,
          ip: req.ip,
          path: req.originalUrl
        });

        return res.status(403).json({
          error: 'Admin panel access denied',
          code: 'ADMIN_ACCESS_DENIED',
          message: 'Your account does not have admin panel access privileges'
        });
      }

      // Log admin panel access
      logger.info('Admin panel access granted', {
        userId: req.user._id,
        username: req.user.username,
        role: req.user.role,
        ip: req.ip,
        path: req.originalUrl
      });

      next();
    };
  }

  /**
   * Optional authentication (doesn't fail if no token)
   */
  optionalAuth() {
    return async (req, res, next) => {
      try {
        const authHeader = req.headers.authorization;
        const token = authHeader && authHeader.split(' ')[1];

        if (!token) {
          return next(); // No token, continue without user
        }

        const decoded = jwt.verify(token, this.jwtSecret, {
          issuer: this.jwtIssuer,
          audience: this.jwtAudience
        });

        const User = require('../../models/User');
        const user = await User.findById(decoded.userId);
        
        if (user && user.status === 'active') {
          req.user = user;
          req.tokenData = decoded;
        }

        next();
      } catch (error) {
        // Ignore auth errors for optional auth
        next();
      }
    };
  }

  /**
   * Session validation middleware
   */
  validateSession() {
    return async (req, res, next) => {
      try {
        if (!req.user) {
          return next();
        }

        // Check if user account is still active
        const User = require('../../models/User');
        const currentUser = await User.findById(req.user._id);

        if (!currentUser) {
          return res.status(401).json({
            error: 'User account not found',
            code: 'USER_NOT_FOUND'
          });
        }

        if (currentUser.status !== 'active') {
          return res.status(403).json({
            error: 'User account is not active',
            code: 'ACCOUNT_INACTIVE'
          });
        }

        // Update user object with latest data
        req.user = currentUser;
        next();
      } catch (error) {
        logger.error('Session validation failed', { error: error.message });
        res.status(500).json({
          error: 'Session validation failed',
          code: 'SESSION_VALIDATION_ERROR'
        });
      }
    };
  }
}

module.exports = AuthMiddleware;
