const { logger } = require('../../utils/logger');

/**
 * Error Manager
 * Handles all error handling and logging
 */
class ErrorManager {
  constructor(app) {
    this.app = app;
  }

  /**
   * Setup error handling middleware
   */
  async setup() {
    try {
      logger.info('Setting up error handling...');

      // Setup global error handlers
      this.setupGlobalErrorHandlers();
      
      // Setup Express error middleware
      this.setupExpressErrorHandling();

      logger.info('Error handling setup completed successfully');
    } catch (error) {
      logger.error('Error handling setup failed', { error: error.message });
      throw error;
    }
  }

  /**
   * Setup global Node.js error handlers
   */
  setupGlobalErrorHandlers() {
    // Handle uncaught exceptions
    process.on('uncaughtException', (error) => {
      logger.error('Uncaught Exception', {
        error: error.message,
        stack: error.stack,
        pid: process.pid
      });

      // Graceful shutdown
      this.gracefulShutdown('uncaughtException');
    });

    // Handle unhandled promise rejections
    process.on('unhandledRejection', (reason, promise) => {
      logger.error('Unhandled Promise Rejection', {
        reason: reason instanceof Error ? reason.message : reason,
        stack: reason instanceof Error ? reason.stack : undefined,
        promise: promise.toString(),
        pid: process.pid
      });

      // Graceful shutdown
      this.gracefulShutdown('unhandledRejection');
    });

    // Handle warnings
    process.on('warning', (warning) => {
      logger.warn('Node.js Warning', {
        name: warning.name,
        message: warning.message,
        stack: warning.stack
      });
    });
  }

  /**
   * Setup Express error handling middleware
   */
  setupExpressErrorHandling() {
    // 404 handler for non-API routes
    this.app.use((req, res, next) => {
      if (!req.originalUrl.startsWith('/api/')) {
        return next();
      }

      const error = new Error(`Route not found: ${req.method} ${req.originalUrl}`);
      error.status = 404;
      error.code = 'ROUTE_NOT_FOUND';
      next(error);
    });

    // Main error handler
    this.app.use((error, req, res, next) => {
      this.handleError(error, req, res, next);
    });
  }

  /**
   * Main error handling function
   */
  handleError(error, req, res, next) {
    // Set default error status
    const status = error.status || error.statusCode || 500;
    const code = error.code || 'INTERNAL_SERVER_ERROR';

    // Log the error
    this.logError(error, req);

    // Don't send error response if headers already sent
    if (res.headersSent) {
      return next(error);
    }

    // Prepare error response
    const errorResponse = this.prepareErrorResponse(error, req, status, code);

    // Send error response
    res.status(status).json(errorResponse);
  }

  /**
   * Log error with context
   */
  logError(error, req) {
    const logLevel = this.getLogLevel(error.status || 500);
    const logData = {
      error: error.message,
      stack: error.stack,
      status: error.status || 500,
      code: error.code,
      method: req.method,
      url: req.originalUrl,
      ip: req.ip,
      userAgent: req.get('User-Agent'),
      userId: req.user?.id,
      timestamp: new Date().toISOString()
    };

    logger[logLevel]('Request error', logData);
  }

  /**
   * Get appropriate log level based on error status
   */
  getLogLevel(status) {
    if (status >= 500) return 'error';
    if (status >= 400) return 'warn';
    return 'info';
  }

  /**
   * Prepare error response based on environment
   */
  prepareErrorResponse(error, req, status, code) {
    const isProduction = process.env.NODE_ENV === 'production';
    const isDevelopment = process.env.NODE_ENV === 'development';

    const baseResponse = {
      error: this.getSafeErrorMessage(error, status),
      code: code,
      status: status,
      timestamp: new Date().toISOString(),
      path: req.originalUrl,
      method: req.method
    };

    // Add additional info in development
    if (isDevelopment) {
      baseResponse.stack = error.stack;
      baseResponse.details = error.details || null;
    }

    // Add request ID if available
    if (req.id) {
      baseResponse.requestId = req.id;
    }

    return baseResponse;
  }

  /**
   * Get safe error message (avoid leaking sensitive info)
   */
  getSafeErrorMessage(error, status) {
    // For client errors (4xx), use the original message
    if (status >= 400 && status < 500) {
      return error.message || 'Client error';
    }

    // For server errors (5xx), use generic message in production
    if (process.env.NODE_ENV === 'production') {
      return 'Internal server error';
    }

    return error.message || 'Server error';
  }

  /**
   * Create standardized error objects
   */
  createError(message, status = 500, code = null, details = null) {
    const error = new Error(message);
    error.status = status;
    error.code = code;
    error.details = details;
    return error;
  }

  /**
   * Create validation error
   */
  createValidationError(message, errors = []) {
    const error = this.createError(message, 400, 'VALIDATION_ERROR');
    error.details = { validationErrors: errors };
    return error;
  }

  /**
   * Create authentication error
   */
  createAuthError(message = 'Authentication required') {
    return this.createError(message, 401, 'AUTHENTICATION_ERROR');
  }

  /**
   * Create authorization error
   */
  createAuthorizationError(message = 'Insufficient permissions') {
    return this.createError(message, 403, 'AUTHORIZATION_ERROR');
  }

  /**
   * Create not found error
   */
  createNotFoundError(resource = 'Resource') {
    return this.createError(`${resource} not found`, 404, 'NOT_FOUND');
  }

  /**
   * Create conflict error
   */
  createConflictError(message) {
    return this.createError(message, 409, 'CONFLICT');
  }

  /**
   * Create rate limit error
   */
  createRateLimitError(message = 'Too many requests') {
    return this.createError(message, 429, 'RATE_LIMIT_EXCEEDED');
  }

  /**
   * Graceful shutdown on critical errors
   */
  gracefulShutdown(reason) {
    logger.error(`Initiating graceful shutdown due to: ${reason}`);

    // Give the process some time to finish current requests
    setTimeout(() => {
      process.exit(1);
    }, 5000);
  }

  /**
   * Async error wrapper for route handlers
   */
  asyncHandler(fn) {
    return (req, res, next) => {
      Promise.resolve(fn(req, res, next)).catch(next);
    };
  }

  /**
   * Validation error handler
   */
  handleValidationError(validationResult) {
    if (!validationResult.isEmpty()) {
      const errors = validationResult.array();
      throw this.createValidationError('Validation failed', errors);
    }
  }
}

module.exports = ErrorManager;
