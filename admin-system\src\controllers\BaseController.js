const responseHandler = require('../utils/ResponseHandler');
const { logger } = require('../../utils/logger');

/**
 * Base Controller Class
 * Provides common functionality for all controllers
 */
class BaseController {
  constructor(model, modelName) {
    this.model = model;
    this.modelName = modelName;
    this.responseHandler = responseHandler;
  }

  /**
   * Get all resources with pagination and filtering
   */
  getAll = responseHandler.asyncHandler(async (req, res) => {
    try {
      const { page, limit, skip } = responseHandler.validatePagination(req);
      const query = responseHandler.createSearchQuery(req, this.getSearchFields());
      const sort = responseHandler.createSort(req);

      // Apply additional filters
      this.applyFilters(query, req);

      const [data, total] = await Promise.all([
        this.model.find(query)
          .populate(this.getPopulateFields())
          .sort(sort)
          .skip(skip)
          .limit(limit)
          .lean(),
        this.model.countDocuments(query)
      ]);

      const pagination = responseHandler.createPagination(page, limit, total);
      const sanitizedData = responseHandler.sanitizeData(data);

      return responseHandler.paginated(res, sanitizedData, pagination);
    } catch (error) {
      return responseHandler.handleDatabaseError(res, error, {
        action: 'getAll',
        model: this.modelName,
        userId: req.user?._id
      });
    }
  });

  /**
   * Get single resource by ID
   */
  getById = responseHandler.asyncHandler(async (req, res) => {
    try {
      const data = await this.model.findById(req.params.id)
        .populate(this.getPopulateFields())
        .lean();

      if (!data) {
        return responseHandler.notFound(res, this.modelName);
      }

      // Check access permissions
      if (!this.canAccess(data, req.user, 'read')) {
        return responseHandler.forbidden(res);
      }

      const sanitizedData = responseHandler.sanitizeData(data);
      return responseHandler.success(res, sanitizedData);
    } catch (error) {
      return responseHandler.handleDatabaseError(res, error, {
        action: 'getById',
        model: this.modelName,
        id: req.params.id,
        userId: req.user?._id
      });
    }
  });

  /**
   * Create new resource
   */
  create = responseHandler.asyncHandler(async (req, res) => {
    try {
      // Prepare data
      const data = this.prepareCreateData(req.body, req.user);
      
      // Validate business rules
      const validationError = await this.validateCreate(data, req.user);
      if (validationError) {
        return responseHandler.error(res, validationError.message, 400, validationError.code);
      }

      // Create resource
      const resource = new this.model(data);
      await resource.save();

      // Log creation
      this.logAction(req, 'create', resource._id, data);

      const populatedResource = await this.model.findById(resource._id)
        .populate(this.getPopulateFields())
        .lean();

      const sanitizedData = responseHandler.sanitizeData(populatedResource);
      return responseHandler.created(res, sanitizedData);
    } catch (error) {
      return responseHandler.handleDatabaseError(res, error, {
        action: 'create',
        model: this.modelName,
        data: req.body,
        userId: req.user?._id
      });
    }
  });

  /**
   * Update resource by ID
   */
  update = responseHandler.asyncHandler(async (req, res) => {
    try {
      const existingResource = await this.model.findById(req.params.id);
      
      if (!existingResource) {
        return responseHandler.notFound(res, this.modelName);
      }

      // Check access permissions
      if (!this.canAccess(existingResource, req.user, 'update')) {
        return responseHandler.forbidden(res);
      }

      // Prepare update data
      const updateData = this.prepareUpdateData(req.body, req.user, existingResource);
      
      // Validate business rules
      const validationError = await this.validateUpdate(updateData, req.user, existingResource);
      if (validationError) {
        return responseHandler.error(res, validationError.message, 400, validationError.code);
      }

      // Update resource
      const updatedResource = await this.model.findByIdAndUpdate(
        req.params.id,
        updateData,
        { new: true, runValidators: true }
      ).populate(this.getPopulateFields()).lean();

      // Log update
      this.logAction(req, 'update', req.params.id, updateData, existingResource);

      const sanitizedData = responseHandler.sanitizeData(updatedResource);
      return responseHandler.success(res, sanitizedData, 'Resource updated successfully');
    } catch (error) {
      return responseHandler.handleDatabaseError(res, error, {
        action: 'update',
        model: this.modelName,
        id: req.params.id,
        data: req.body,
        userId: req.user?._id
      });
    }
  });

  /**
   * Delete resource by ID
   */
  delete = responseHandler.asyncHandler(async (req, res) => {
    try {
      const resource = await this.model.findById(req.params.id);
      
      if (!resource) {
        return responseHandler.notFound(res, this.modelName);
      }

      // Check access permissions
      if (!this.canAccess(resource, req.user, 'delete')) {
        return responseHandler.forbidden(res);
      }

      // Validate deletion
      const validationError = await this.validateDelete(resource, req.user);
      if (validationError) {
        return responseHandler.error(res, validationError.message, 400, validationError.code);
      }

      // Perform soft delete if supported, otherwise hard delete
      if (this.supportsSoftDelete()) {
        await this.model.findByIdAndUpdate(req.params.id, {
          deletedAt: new Date(),
          deletedBy: req.user._id
        });
      } else {
        await this.model.findByIdAndDelete(req.params.id);
      }

      // Log deletion
      this.logAction(req, 'delete', req.params.id, null, resource);

      return responseHandler.noContent(res);
    } catch (error) {
      return responseHandler.handleDatabaseError(res, error, {
        action: 'delete',
        model: this.modelName,
        id: req.params.id,
        userId: req.user?._id
      });
    }
  });

  // Override these methods in child controllers

  /**
   * Get fields to search in
   */
  getSearchFields() {
    return ['name', 'title'];
  }

  /**
   * Get fields to populate
   */
  getPopulateFields() {
    return '';
  }

  /**
   * Apply additional filters to query
   */
  applyFilters(query, req) {
    // Override in child controllers
  }

  /**
   * Check if user can access resource
   */
  canAccess(resource, user, action) {
    // Override in child controllers
    return true;
  }

  /**
   * Prepare data for creation
   */
  prepareCreateData(data, user) {
    return {
      ...data,
      createdBy: user._id,
      createdAt: new Date()
    };
  }

  /**
   * Prepare data for update
   */
  prepareUpdateData(data, user, existingResource) {
    return {
      ...data,
      updatedBy: user._id,
      updatedAt: new Date()
    };
  }

  /**
   * Validate creation
   */
  async validateCreate(data, user) {
    // Override in child controllers
    return null;
  }

  /**
   * Validate update
   */
  async validateUpdate(data, user, existingResource) {
    // Override in child controllers
    return null;
  }

  /**
   * Validate deletion
   */
  async validateDelete(resource, user) {
    // Override in child controllers
    return null;
  }

  /**
   * Check if model supports soft delete
   */
  supportsSoftDelete() {
    return false;
  }

  /**
   * Log action for audit trail
   */
  logAction(req, action, resourceId, data = null, previousData = null) {
    const auditData = responseHandler.createAuditLog(
      req, 
      action, 
      this.modelName, 
      resourceId, 
      data
    );

    logger.info(`${this.modelName} ${action}`, {
      ...auditData,
      previousData: previousData ? responseHandler.sanitizeData(previousData) : null
    });
  }
}

module.exports = BaseController;
