const express = require('express');
const UserController = require('../controllers/UserController');
const ValidationMiddleware = require('../middleware/ValidationMiddleware');
const AuthMiddleware = require('../middleware/AuthMiddleware');

const router = express.Router();
const validation = new ValidationMiddleware();
const auth = new AuthMiddleware();

/**
 * User Management Routes
 * RESTful API for user operations with proper validation and authorization
 */

// Apply authentication to all routes
router.use(auth.authenticate());

// GET /users - Get all users with pagination and filtering
router.get('/', 
  auth.requirePermission('users', 'read'),
  validation.validatePagination(),
  UserController.getAll
);

// GET /users/stats - Get user statistics
router.get('/stats',
  auth.requirePermission('users', 'read'),
  UserController.getStats
);

// GET /users/:id - Get single user by ID
router.get('/:id',
  auth.requirePermission('users', 'read'),
  validation.validateId(),
  UserController.getById
);

// POST /users - Create new user
router.post('/',
  auth.authorize('super_admin', 'admin'),
  validation.validateUserCreation(),
  UserController.create
);

// PUT /users/:id - Update user
router.put('/:id',
  auth.requirePermission('users', 'update'),
  validation.validateId(),
  validation.validateUserUpdate(),
  UserController.update
);

// DELETE /users/:id - Delete user (soft delete)
router.delete('/:id',
  auth.authorize('super_admin', 'admin'),
  validation.validateId(),
  UserController.delete
);

// POST /users/:id/change-password - Change user password
router.post('/:id/change-password',
  auth.requirePermission('users', 'update'),
  validation.validateId(),
  [
    validation.validation.body('currentPassword')
      .optional()
      .isLength({ min: 1 })
      .withMessage('Current password is required'),
    validation.validation.body('newPassword')
      .isLength({ min: 8, max: 128 })
      .withMessage('New password must be between 8 and 128 characters')
      .matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/)
      .withMessage('New password must contain at least one uppercase letter, one lowercase letter, one number, and one special character'),
    validation.handleValidationErrors()
  ],
  UserController.changePassword
);

// PATCH /users/:id/status - Toggle user status
router.patch('/:id/status',
  auth.authorize('super_admin', 'admin'),
  validation.validateId(),
  UserController.toggleStatus
);

module.exports = router;
