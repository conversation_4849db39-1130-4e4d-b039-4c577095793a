/**
 * API Client
 * Centralized HTTP client for API communication
 */
class ApiClient {
  constructor(baseURL = '/api') {
    this.baseURL = baseURL;
    this.token = localStorage.getItem('adminToken');
    this.csrfToken = null;
    this.defaultHeaders = {
      'Content-Type': 'application/json'
    };
  }

  /**
   * Set authentication token
   */
  setToken(token) {
    this.token = token;
    if (token) {
      localStorage.setItem('adminToken', token);
    } else {
      localStorage.removeItem('adminToken');
    }
  }

  /**
   * Get CSRF token
   */
  async getCsrfToken() {
    try {
      const response = await this.request('GET', '/csrf-token');
      this.csrfToken = response.csrfToken;
      return this.csrfToken;
    } catch (error) {
      console.warn('Failed to get CSRF token:', error);
      return null;
    }
  }

  /**
   * Build headers for request
   */
  buildHeaders(customHeaders = {}) {
    const headers = { ...this.defaultHeaders, ...customHeaders };

    if (this.token) {
      headers.Authorization = `Bearer ${this.token}`;
    }

    if (this.csrfToken) {
      headers['X-CSRF-Token'] = this.csrfToken;
    }

    return headers;
  }

  /**
   * Make HTTP request
   */
  async request(method, endpoint, data = null, customHeaders = {}) {
    const url = `${this.baseURL}${endpoint}`;
    const headers = this.buildHeaders(customHeaders);

    const config = {
      method: method.toUpperCase(),
      headers
    };

    if (data && ['POST', 'PUT', 'PATCH'].includes(config.method)) {
      config.body = JSON.stringify(data);
    }

    try {
      const response = await fetch(url, config);
      
      // Handle different response types
      const contentType = response.headers.get('content-type');
      let responseData;

      if (contentType && contentType.includes('application/json')) {
        responseData = await response.json();
      } else {
        responseData = await response.text();
      }

      if (!response.ok) {
        throw new ApiError(
          responseData.error || responseData.message || 'Request failed',
          response.status,
          responseData.code,
          responseData.details
        );
      }

      return responseData;
    } catch (error) {
      if (error instanceof ApiError) {
        throw error;
      }

      // Network or other errors
      throw new ApiError(
        'Network error or server unavailable',
        0,
        'NETWORK_ERROR'
      );
    }
  }

  /**
   * GET request
   */
  async get(endpoint, params = {}) {
    const queryString = new URLSearchParams(params).toString();
    const url = queryString ? `${endpoint}?${queryString}` : endpoint;
    return this.request('GET', url);
  }

  /**
   * POST request
   */
  async post(endpoint, data) {
    return this.request('POST', endpoint, data);
  }

  /**
   * PUT request
   */
  async put(endpoint, data) {
    return this.request('PUT', endpoint, data);
  }

  /**
   * PATCH request
   */
  async patch(endpoint, data) {
    return this.request('PATCH', endpoint, data);
  }

  /**
   * DELETE request
   */
  async delete(endpoint) {
    return this.request('DELETE', endpoint);
  }

  /**
   * Upload file
   */
  async upload(endpoint, formData) {
    const headers = {};
    if (this.token) {
      headers.Authorization = `Bearer ${this.token}`;
    }
    if (this.csrfToken) {
      headers['X-CSRF-Token'] = this.csrfToken;
    }

    const response = await fetch(`${this.baseURL}${endpoint}`, {
      method: 'POST',
      headers,
      body: formData
    });

    const responseData = await response.json();

    if (!response.ok) {
      throw new ApiError(
        responseData.error || 'Upload failed',
        response.status,
        responseData.code,
        responseData.details
      );
    }

    return responseData;
  }

  /**
   * Handle authentication errors
   */
  handleAuthError(error) {
    if (error.status === 401) {
      this.setToken(null);
      window.location.href = '/admin/login';
    }
  }
}

/**
 * API Error class
 */
class ApiError extends Error {
  constructor(message, status, code, details) {
    super(message);
    this.name = 'ApiError';
    this.status = status;
    this.code = code;
    this.details = details;
  }
}

/**
 * Create singleton instance
 */
const apiClient = new ApiClient();

// Auto-refresh CSRF token
apiClient.getCsrfToken();

// Export for use in other modules
window.ApiClient = ApiClient;
window.ApiError = ApiError;
window.apiClient = apiClient;
