const { body, query, param, validationResult } = require('express-validator');
const { logger } = require('../../utils/logger');

/**
 * Validation Middleware Collection
 * Centralized input validation with consistent error handling
 */
class ValidationMiddleware {
  constructor() {
    this.commonValidations = this.getCommonValidations();
  }

  /**
   * Handle validation errors
   */
  handleValidationErrors() {
    return (req, res, next) => {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        logger.warn('Validation failed', {
          errors: errors.array(),
          endpoint: req.originalUrl,
          method: req.method,
          ip: req.ip,
          userId: req.user?._id
        });

        return res.status(400).json({
          error: 'Validation failed',
          code: 'VALIDATION_ERROR',
          details: errors.array().map(error => ({
            field: error.param,
            message: error.msg,
            value: error.value
          }))
        });
      }
      next();
    };
  }

  /**
   * Common validation rules
   */
  getCommonValidations() {
    return {
      email: body('email')
        .isEmail()
        .normalizeEmail()
        .withMessage('Valid email is required'),

      password: body('password')
        .isLength({ min: 8, max: 128 })
        .withMessage('Password must be between 8 and 128 characters')
        .matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/)
        .withMessage('Password must contain at least one uppercase letter, one lowercase letter, one number, and one special character'),

      username: body('username')
        .isLength({ min: 3, max: 50 })
        .withMessage('Username must be between 3 and 50 characters')
        .matches(/^[a-zA-Z0-9_-]+$/)
        .withMessage('Username can only contain letters, numbers, underscores, and hyphens'),

      name: (field) => body(field)
        .isLength({ min: 1, max: 100 })
        .trim()
        .escape()
        .withMessage(`${field} must be between 1 and 100 characters`),

      id: (field = 'id') => param(field)
        .isMongoId()
        .withMessage('Invalid ID format'),

      pagination: [
        query('page')
          .optional()
          .isInt({ min: 1 })
          .toInt()
          .withMessage('Page must be a positive integer'),
        query('limit')
          .optional()
          .isInt({ min: 1, max: 100 })
          .toInt()
          .withMessage('Limit must be between 1 and 100'),
        query('sort')
          .optional()
          .isString()
          .trim()
          .withMessage('Sort must be a string'),
        query('order')
          .optional()
          .isIn(['asc', 'desc'])
          .withMessage('Order must be asc or desc')
      ]
    };
  }

  /**
   * User creation validation
   */
  validateUserCreation() {
    return [
      this.commonValidations.username,
      this.commonValidations.email,
      this.commonValidations.name('firstName'),
      this.commonValidations.name('lastName'),
      body('role')
        .isIn(['admin', 'editor', 'viewer'])
        .withMessage('Role must be admin, editor, or viewer'),
      body('department')
        .optional()
        .isLength({ max: 100 })
        .trim()
        .escape()
        .withMessage('Department must be less than 100 characters'),
      this.handleValidationErrors()
    ];
  }

  /**
   * User update validation
   */
  validateUserUpdate() {
    return [
      this.commonValidations.id(),
      body('username')
        .optional()
        .isLength({ min: 3, max: 50 })
        .matches(/^[a-zA-Z0-9_-]+$/)
        .withMessage('Username must be between 3 and 50 characters and contain only letters, numbers, underscores, and hyphens'),
      body('email')
        .optional()
        .isEmail()
        .normalizeEmail()
        .withMessage('Valid email is required'),
      body('firstName')
        .optional()
        .isLength({ min: 1, max: 100 })
        .trim()
        .escape()
        .withMessage('First name must be between 1 and 100 characters'),
      body('lastName')
        .optional()
        .isLength({ min: 1, max: 100 })
        .trim()
        .escape()
        .withMessage('Last name must be between 1 and 100 characters'),
      body('role')
        .optional()
        .isIn(['admin', 'editor', 'viewer'])
        .withMessage('Role must be admin, editor, or viewer'),
      body('status')
        .optional()
        .isIn(['active', 'inactive', 'suspended'])
        .withMessage('Status must be active, inactive, or suspended'),
      this.handleValidationErrors()
    ];
  }

  /**
   * Login validation
   */
  validateLogin() {
    return [
      body('email')
        .isEmail()
        .normalizeEmail()
        .withMessage('Valid email is required'),
      body('password')
        .notEmpty()
        .withMessage('Password is required'),
      body('mfaToken')
        .optional()
        .isLength({ min: 6, max: 6 })
        .isNumeric()
        .withMessage('MFA token must be 6 digits'),
      this.handleValidationErrors()
    ];
  }

  /**
   * Content creation validation
   */
  validateContentCreation() {
    return [
      body('title')
        .isLength({ min: 1, max: 200 })
        .trim()
        .withMessage('Title must be between 1 and 200 characters'),
      body('content')
        .isLength({ min: 1 })
        .withMessage('Content is required'),
      body('excerpt')
        .optional()
        .isLength({ max: 500 })
        .trim()
        .withMessage('Excerpt must be less than 500 characters'),
      body('status')
        .isIn(['draft', 'published', 'archived'])
        .withMessage('Status must be draft, published, or archived'),
      body('categories')
        .optional()
        .isArray()
        .withMessage('Categories must be an array'),
      body('tags')
        .optional()
        .isArray()
        .withMessage('Tags must be an array'),
      body('publishDate')
        .optional()
        .isISO8601()
        .toDate()
        .withMessage('Publish date must be a valid date'),
      this.handleValidationErrors()
    ];
  }

  /**
   * Content update validation
   */
  validateContentUpdate() {
    return [
      this.commonValidations.id(),
      body('title')
        .optional()
        .isLength({ min: 1, max: 200 })
        .trim()
        .withMessage('Title must be between 1 and 200 characters'),
      body('content')
        .optional()
        .isLength({ min: 1 })
        .withMessage('Content cannot be empty'),
      body('excerpt')
        .optional()
        .isLength({ max: 500 })
        .trim()
        .withMessage('Excerpt must be less than 500 characters'),
      body('status')
        .optional()
        .isIn(['draft', 'published', 'archived'])
        .withMessage('Status must be draft, published, or archived'),
      body('categories')
        .optional()
        .isArray()
        .withMessage('Categories must be an array'),
      body('tags')
        .optional()
        .isArray()
        .withMessage('Tags must be an array'),
      body('publishDate')
        .optional()
        .isISO8601()
        .toDate()
        .withMessage('Publish date must be a valid date'),
      this.handleValidationErrors()
    ];
  }

  /**
   * Dating profile validation
   */
  validateDatingProfile() {
    return [
      body('name')
        .isLength({ min: 1, max: 100 })
        .trim()
        .withMessage('Name must be between 1 and 100 characters'),
      body('age')
        .isInt({ min: 18, max: 120 })
        .withMessage('Age must be between 18 and 120'),
      body('location')
        .optional()
        .isLength({ max: 100 })
        .trim()
        .withMessage('Location must be less than 100 characters'),
      body('bio')
        .optional()
        .isLength({ max: 1000 })
        .trim()
        .withMessage('Bio must be less than 1000 characters'),
      body('interests')
        .optional()
        .isArray()
        .withMessage('Interests must be an array'),
      body('lookingFor')
        .optional()
        .isLength({ max: 500 })
        .trim()
        .withMessage('Looking for must be less than 500 characters'),
      this.handleValidationErrors()
    ];
  }

  /**
   * Search validation
   */
  validateSearch() {
    return [
      query('q')
        .optional()
        .isLength({ min: 1, max: 100 })
        .trim()
        .withMessage('Search query must be between 1 and 100 characters'),
      query('category')
        .optional()
        .isString()
        .trim()
        .withMessage('Category must be a string'),
      query('tag')
        .optional()
        .isString()
        .trim()
        .withMessage('Tag must be a string'),
      query('status')
        .optional()
        .isIn(['draft', 'published', 'archived'])
        .withMessage('Status must be draft, published, or archived'),
      ...this.commonValidations.pagination,
      this.handleValidationErrors()
    ];
  }

  /**
   * File upload validation
   */
  validateFileUpload() {
    return [
      body('description')
        .optional()
        .isLength({ max: 200 })
        .trim()
        .withMessage('Description must be less than 200 characters'),
      this.handleValidationErrors()
    ];
  }

  /**
   * Generic ID validation
   */
  validateId(paramName = 'id') {
    return [
      this.commonValidations.id(paramName),
      this.handleValidationErrors()
    ];
  }

  /**
   * Pagination validation
   */
  validatePagination() {
    return [
      ...this.commonValidations.pagination,
      this.handleValidationErrors()
    ];
  }
}

module.exports = ValidationMiddleware;
