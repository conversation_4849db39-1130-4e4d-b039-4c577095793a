const nodemailer = require('nodemailer');
const { logger } = require('../utils/logger');

/**
 * Enterprise Email Service
 * Handles secure email notifications and alerts
 */

class EmailService {
  constructor() {
    this.transporter = null;
    this.init();
  }

  init() {
    try {
      // Create transporter
      this.transporter = nodemailer.createTransporter({
        host: process.env.SMTP_HOST,
        port: parseInt(process.env.SMTP_PORT) || 587,
        secure: process.env.SMTP_SECURE === 'true',
        auth: {
          user: process.env.SMTP_USER,
          pass: process.env.SMTP_PASS
        },
        tls: {
          rejectUnauthorized: process.env.NODE_ENV === 'production'
        }
      });

      // Verify connection
      this.transporter.verify((error, success) => {
        if (error) {
          logger.error('Email service connection failed', { error: error.message });
        } else {
          logger.info('Email service connected successfully');
        }
      });

    } catch (error) {
      logger.error('Email service initialization failed', { error: error.message });
    }
  }

  async sendEmail(to, subject, html, text = null) {
    try {
      if (!this.transporter) {
        throw new Error('Email service not initialized');
      }

      const mailOptions = {
        from: `"Admin Panel" <${process.env.SMTP_USER}>`,
        to,
        subject,
        html,
        text: text || this.stripHtml(html)
      };

      const result = await this.transporter.sendMail(mailOptions);
      
      logger.info('Email sent successfully', {
        to,
        subject,
        messageId: result.messageId
      });

      return result;
    } catch (error) {
      logger.error('Failed to send email', {
        error: error.message,
        to,
        subject
      });
      throw error;
    }
  }

  async sendPasswordResetEmail(email, resetToken) {
    const resetUrl = `${process.env.ADMIN_URL || 'http://localhost:3000'}/admin/reset-password?token=${resetToken}`;
    
    const html = `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <title>Password Reset Request</title>
        <style>
          body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
          .container { max-width: 600px; margin: 0 auto; padding: 20px; }
          .header { background: #0d6efd; color: white; padding: 20px; text-align: center; }
          .content { padding: 20px; background: #f8f9fa; }
          .button { display: inline-block; padding: 12px 24px; background: #0d6efd; color: white; text-decoration: none; border-radius: 5px; margin: 20px 0; }
          .warning { background: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; border-radius: 5px; margin: 20px 0; }
          .footer { text-align: center; padding: 20px; color: #666; font-size: 12px; }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <h1>🔒 Password Reset Request</h1>
          </div>
          <div class="content">
            <h2>Hello,</h2>
            <p>We received a request to reset your password for the Admin Panel. If you made this request, click the button below to reset your password:</p>
            
            <div style="text-align: center;">
              <a href="${resetUrl}" class="button">Reset Password</a>
            </div>
            
            <div class="warning">
              <strong>⚠️ Security Notice:</strong>
              <ul>
                <li>This link will expire in 10 minutes</li>
                <li>If you didn't request this reset, please ignore this email</li>
                <li>Never share this link with anyone</li>
                <li>Our team will never ask for your password via email</li>
              </ul>
            </div>
            
            <p>If the button doesn't work, copy and paste this link into your browser:</p>
            <p style="word-break: break-all; background: #e9ecef; padding: 10px; border-radius: 3px;">
              ${resetUrl}
            </p>
            
            <p>If you have any questions or concerns, please contact your system administrator.</p>
            
            <p>Best regards,<br>Admin Panel Security Team</p>
          </div>
          <div class="footer">
            <p>This is an automated message. Please do not reply to this email.</p>
            <p>© ${new Date().getFullYear()} Admin Panel. All rights reserved.</p>
          </div>
        </div>
      </body>
      </html>
    `;

    return this.sendEmail(email, 'Password Reset Request - Admin Panel', html);
  }

  async sendSecurityAlert(email, alertType, details) {
    const alertMessages = {
      'login_failure': 'Multiple failed login attempts detected',
      'suspicious_activity': 'Suspicious activity detected on your account',
      'account_locked': 'Your account has been temporarily locked',
      'password_changed': 'Your password has been changed',
      'mfa_enabled': 'Multi-factor authentication has been enabled',
      'privilege_escalation': 'Your account privileges have been modified'
    };

    const subject = `🚨 Security Alert: ${alertMessages[alertType] || 'Security Event'}`;
    
    const html = `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <title>Security Alert</title>
        <style>
          body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
          .container { max-width: 600px; margin: 0 auto; padding: 20px; }
          .header { background: #dc3545; color: white; padding: 20px; text-align: center; }
          .content { padding: 20px; background: #f8f9fa; }
          .alert { background: #f8d7da; border: 1px solid #f5c6cb; padding: 15px; border-radius: 5px; margin: 20px 0; }
          .details { background: #e9ecef; padding: 15px; border-radius: 5px; margin: 20px 0; }
          .footer { text-align: center; padding: 20px; color: #666; font-size: 12px; }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <h1>🚨 Security Alert</h1>
          </div>
          <div class="content">
            <div class="alert">
              <strong>Alert:</strong> ${alertMessages[alertType] || 'Security event detected'}
            </div>
            
            <h3>Event Details:</h3>
            <div class="details">
              <p><strong>Time:</strong> ${new Date().toLocaleString()}</p>
              <p><strong>IP Address:</strong> ${details.ipAddress || 'Unknown'}</p>
              <p><strong>User Agent:</strong> ${details.userAgent || 'Unknown'}</p>
              ${details.location ? `<p><strong>Location:</strong> ${details.location}</p>` : ''}
              ${details.description ? `<p><strong>Description:</strong> ${details.description}</p>` : ''}
            </div>
            
            <h3>Recommended Actions:</h3>
            <ul>
              <li>Review your recent account activity</li>
              <li>Change your password if you suspect unauthorized access</li>
              <li>Enable multi-factor authentication if not already active</li>
              <li>Contact your system administrator if you have concerns</li>
            </ul>
            
            <p>If this activity was authorized by you, no action is required.</p>
            
            <p>Best regards,<br>Admin Panel Security Team</p>
          </div>
          <div class="footer">
            <p>This is an automated security notification. Please do not reply to this email.</p>
            <p>© ${new Date().getFullYear()} Admin Panel. All rights reserved.</p>
          </div>
        </div>
      </body>
      </html>
    `;

    return this.sendEmail(email, subject, html);
  }

  async sendWelcomeEmail(email, username, tempPassword) {
    const loginUrl = `${process.env.ADMIN_URL || 'http://localhost:3000'}/admin`;
    
    const html = `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <title>Welcome to Admin Panel</title>
        <style>
          body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
          .container { max-width: 600px; margin: 0 auto; padding: 20px; }
          .header { background: #0d6efd; color: white; padding: 20px; text-align: center; }
          .content { padding: 20px; background: #f8f9fa; }
          .credentials { background: #d1ecf1; border: 1px solid #bee5eb; padding: 15px; border-radius: 5px; margin: 20px 0; }
          .button { display: inline-block; padding: 12px 24px; background: #0d6efd; color: white; text-decoration: none; border-radius: 5px; margin: 20px 0; }
          .warning { background: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; border-radius: 5px; margin: 20px 0; }
          .footer { text-align: center; padding: 20px; color: #666; font-size: 12px; }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <h1>🎉 Welcome to Admin Panel</h1>
          </div>
          <div class="content">
            <h2>Hello ${username},</h2>
            <p>Your admin account has been created successfully. You can now access the admin panel with the following credentials:</p>
            
            <div class="credentials">
              <p><strong>Username:</strong> ${username}</p>
              <p><strong>Email:</strong> ${email}</p>
              <p><strong>Temporary Password:</strong> <code>${tempPassword}</code></p>
            </div>
            
            <div style="text-align: center;">
              <a href="${loginUrl}" class="button">Access Admin Panel</a>
            </div>
            
            <div class="warning">
              <strong>⚠️ Important Security Steps:</strong>
              <ul>
                <li>Change your password immediately after first login</li>
                <li>Enable multi-factor authentication for enhanced security</li>
                <li>Never share your login credentials with anyone</li>
                <li>Log out when finished using the admin panel</li>
              </ul>
            </div>
            
            <h3>Getting Started:</h3>
            <ol>
              <li>Click the "Access Admin Panel" button above</li>
              <li>Log in with your credentials</li>
              <li>Complete the security setup wizard</li>
              <li>Explore the admin features available to your role</li>
            </ol>
            
            <p>If you have any questions or need assistance, please contact your system administrator.</p>
            
            <p>Best regards,<br>Admin Panel Team</p>
          </div>
          <div class="footer">
            <p>This is an automated message. Please do not reply to this email.</p>
            <p>© ${new Date().getFullYear()} Admin Panel. All rights reserved.</p>
          </div>
        </div>
      </body>
      </html>
    `;

    return this.sendEmail(email, 'Welcome to Admin Panel - Account Created', html);
  }

  async sendSystemAlert(recipients, alertType, details) {
    const subject = `🚨 System Alert: ${alertType}`;
    
    const html = `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <title>System Alert</title>
        <style>
          body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
          .container { max-width: 600px; margin: 0 auto; padding: 20px; }
          .header { background: #dc3545; color: white; padding: 20px; text-align: center; }
          .content { padding: 20px; background: #f8f9fa; }
          .alert { background: #f8d7da; border: 1px solid #f5c6cb; padding: 15px; border-radius: 5px; margin: 20px 0; }
          .details { background: #e9ecef; padding: 15px; border-radius: 5px; margin: 20px 0; font-family: monospace; }
          .footer { text-align: center; padding: 20px; color: #666; font-size: 12px; }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <h1>🚨 System Alert</h1>
          </div>
          <div class="content">
            <div class="alert">
              <strong>Alert Type:</strong> ${alertType}
            </div>
            
            <h3>Alert Details:</h3>
            <div class="details">
              <pre>${JSON.stringify(details, null, 2)}</pre>
            </div>
            
            <p><strong>Timestamp:</strong> ${new Date().toISOString()}</p>
            <p><strong>Server:</strong> ${process.env.SERVER_INSTANCE || 'default'}</p>
            <p><strong>Environment:</strong> ${process.env.NODE_ENV || 'development'}</p>
            
            <p>Please investigate this alert and take appropriate action if necessary.</p>
            
            <p>Best regards,<br>System Monitoring</p>
          </div>
          <div class="footer">
            <p>This is an automated system alert. Please do not reply to this email.</p>
            <p>© ${new Date().getFullYear()} Admin Panel. All rights reserved.</p>
          </div>
        </div>
      </body>
      </html>
    `;

    // Send to multiple recipients
    const promises = recipients.map(email => 
      this.sendEmail(email, subject, html)
    );

    return Promise.allSettled(promises);
  }

  stripHtml(html) {
    return html.replace(/<[^>]*>/g, '').replace(/\s+/g, ' ').trim();
  }
}

// Create singleton instance
const emailService = new EmailService();

module.exports = emailService;
