<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Blog Website - Read Only</title>
    
    <!-- Security Headers -->
    <meta http-equiv="Content-Security-Policy" content="default-src 'self'; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline'; img-src 'self' data: https:;">
    <meta http-equiv="X-Content-Type-Options" content="nosniff">
    <meta http-equiv="X-Frame-Options" content="SAMEORIGIN">
    
    <!-- Stylesheets -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="css/website.css" rel="stylesheet">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="/">Blog Website</a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link active" href="/">Home</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/profile.html">Dating Profile</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/about">About</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/contact">Contact</a>
                    </li>
                </ul>
                <div class="navbar-nav">
                    <div class="nav-item">
                        <span class="navbar-text">
                            <small>🔒 Read-Only Mode</small>
                        </span>
                    </div>
                </div>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <div class="container mt-4">
        <!-- Alert for Admin Access -->
        <div class="alert alert-info alert-dismissible fade show" role="alert">
            <strong>📝 Content Management:</strong> 
            This website is in read-only mode. To edit content, please access the 
            <a href="/admin" class="alert-link">secure admin panel</a>.
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>

        <!-- Hero Section -->
        <div class="hero-section bg-light p-5 rounded mb-4">
            <div class="container-fluid py-5">
                <h1 class="display-5 fw-bold">Welcome to Our Blog</h1>
                <p class="col-md-8 fs-4">
                    Discover amazing content and insights. This website displays published content 
                    in a secure, read-only format.
                </p>
                <div class="d-flex gap-2">
                    <button class="btn btn-primary btn-lg" onclick="loadPosts()">
                        View Latest Posts
                    </button>
                    <a href="/admin" class="btn btn-outline-secondary btn-lg">
                        Admin Panel
                    </a>
                </div>
            </div>
        </div>

        <!-- Search Section -->
        <div class="row mb-4">
            <div class="col-md-8 mx-auto">
                <div class="input-group">
                    <input type="text" class="form-control" id="searchInput" placeholder="Search posts...">
                    <button class="btn btn-outline-secondary" type="button" onclick="searchPosts()">
                        Search
                    </button>
                </div>
            </div>
        </div>

        <!-- Categories and Tags -->
        <div class="row mb-4">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5>Categories</h5>
                    </div>
                    <div class="card-body">
                        <div id="categories-list">
                            <div class="text-center">
                                <div class="spinner-border spinner-border-sm" role="status">
                                    <span class="visually-hidden">Loading...</span>
                                </div>
                                Loading categories...
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5>Popular Tags</h5>
                    </div>
                    <div class="card-body">
                        <div id="tags-list">
                            <div class="text-center">
                                <div class="spinner-border spinner-border-sm" role="status">
                                    <span class="visually-hidden">Loading...</span>
                                </div>
                                Loading tags...
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Posts Section -->
        <div class="row">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <h2>Latest Posts</h2>
                    <div class="text-muted">
                        <small>Content managed through admin panel</small>
                    </div>
                </div>
                <div id="posts-container">
                    <div class="text-center py-5">
                        <div class="spinner-border" role="status">
                            <span class="visually-hidden">Loading...</span>
                        </div>
                        <p class="mt-2">Loading posts...</p>
                    </div>
                </div>
                
                <!-- Pagination -->
                <nav aria-label="Posts pagination" class="mt-4">
                    <ul class="pagination justify-content-center" id="pagination">
                        <!-- Pagination will be populated by JavaScript -->
                    </ul>
                </nav>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <footer class="bg-dark text-light mt-5 py-4">
        <div class="container">
            <div class="row">
                <div class="col-md-6">
                    <h5>Blog Website</h5>
                    <p>Secure content management with read-only public access.</p>
                </div>
                <div class="col-md-6 text-md-end">
                    <p class="mb-0">
                        <small>
                            🔒 Content editing available through 
                            <a href="/admin" class="text-light">admin panel</a>
                        </small>
                    </p>
                    <p class="mb-0">
                        <small>&copy; 2024 Blog Website. All rights reserved.</small>
                    </p>
                </div>
            </div>
        </div>
    </footer>

    <!-- No Edit Modal (shown when user tries to edit) -->
    <div class="modal fade" id="noEditModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">🔒 Editing Not Available</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <p>Content editing is not available on the public website for security reasons.</p>
                    <p>To edit content, please:</p>
                    <ol>
                        <li>Access the <strong>secure admin panel</strong></li>
                        <li>Log in with your admin credentials</li>
                        <li>Use the content management system</li>
                    </ol>
                    <div class="alert alert-info">
                        <strong>Security Note:</strong> This separation ensures that only authorized 
                        users can modify content through the secure admin interface.
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                        Close
                    </button>
                    <a href="/admin" class="btn btn-primary">
                        Go to Admin Panel
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="js/website.js"></script>
</body>
</html>
