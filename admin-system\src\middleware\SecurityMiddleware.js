const helmet = require('helmet');
const rateLimit = require('express-rate-limit');
const slowDown = require('express-slow-down');
const mongoSanitize = require('express-mongo-sanitize');
const xss = require('xss');
const hpp = require('hpp');
const { logger } = require('../../utils/logger');

/**
 * Security Middleware Collection
 * Centralized security middleware with optimized configuration
 */
class SecurityMiddleware {
  constructor(config) {
    this.config = config || {};
  }

  /**
   * Security headers middleware
   */
  getSecurityHeaders() {
    return helmet({
      contentSecurityPolicy: {
        directives: {
          defaultSrc: ["'self'"],
          styleSrc: ["'self'", "'unsafe-inline'", 'https://cdn.jsdelivr.net'],
          scriptSrc: [
            "'self'",
            "'unsafe-inline'", // Required for some admin functionality
            'https://cdn.jsdelivr.net',
            'https://cdnjs.cloudflare.com',
            'https://challenges.cloudflare.com'
          ],
          imgSrc: ["'self'", 'data:', 'https:', 'blob:'],
          fontSrc: ["'self'", 'https://cdn.jsdelivr.net'],
          connectSrc: ["'self'"],
          frameSrc: ["'none'"],
          objectSrc: ["'none'"],
          mediaSrc: ["'self'"],
          manifestSrc: ["'self'"]
        }
      },
      crossOriginEmbedderPolicy: false,
      hsts: {
        maxAge: 31536000,
        includeSubDomains: true,
        preload: true
      }
    });
  }

  /**
   * Rate limiting middleware
   */
  getRateLimit(options = {}) {
    const defaultOptions = {
      windowMs: 15 * 60 * 1000, // 15 minutes
      max: 100, // Limit each IP to 100 requests per windowMs
      message: {
        error: 'Too many requests from this IP',
        code: 'RATE_LIMIT_EXCEEDED'
      },
      standardHeaders: true,
      legacyHeaders: false,
      handler: (req, res) => {
        logger.warn('Rate limit exceeded', {
          ip: req.ip,
          endpoint: req.originalUrl,
          userAgent: req.get('User-Agent')
        });
        
        res.status(429).json({
          error: 'Too many requests from this IP',
          code: 'RATE_LIMIT_EXCEEDED',
          retryAfter: Math.round(options.windowMs / 1000) || 900
        });
      }
    };

    return rateLimit({ ...defaultOptions, ...options });
  }

  /**
   * API-specific rate limiting
   */
  getApiRateLimit() {
    return this.getRateLimit({
      windowMs: 15 * 60 * 1000, // 15 minutes
      max: 200, // Higher limit for API
      message: {
        error: 'API rate limit exceeded',
        code: 'API_RATE_LIMIT_EXCEEDED'
      }
    });
  }

  /**
   * Login rate limiting
   */
  getLoginRateLimit() {
    return this.getRateLimit({
      windowMs: 15 * 60 * 1000, // 15 minutes
      max: 5, // Strict limit for login attempts
      skipSuccessfulRequests: true,
      message: {
        error: 'Too many login attempts',
        code: 'LOGIN_RATE_LIMIT_EXCEEDED'
      }
    });
  }

  /**
   * Slow down middleware for suspicious activity
   */
  getSlowDown() {
    return slowDown({
      windowMs: 15 * 60 * 1000, // 15 minutes
      delayAfter: 50, // Allow 50 requests per windowMs without delay
      delayMs: 500, // Add 500ms delay per request after delayAfter
      maxDelayMs: 20000, // Maximum delay of 20 seconds
      onLimitReached: (req, res, options) => {
        logger.warn('Speed limit reached - adding delays', {
          ip: req.ip,
          endpoint: req.originalUrl,
          delay: options.delay
        });
      }
    });
  }

  /**
   * Input sanitization middleware
   */
  getSanitizeInput() {
    return (req, res, next) => {
      try {
        // MongoDB injection protection
        mongoSanitize.sanitize(req.body);
        mongoSanitize.sanitize(req.query);
        mongoSanitize.sanitize(req.params);

        // XSS protection for string values
        const sanitizeValue = (value) => {
          if (typeof value === 'string') {
            return xss(value, {
              whiteList: {}, // No HTML tags allowed
              stripIgnoreTag: true,
              stripIgnoreTagBody: ['script']
            });
          }
          return value;
        };

        const sanitizeObject = (obj) => {
          if (obj && typeof obj === 'object') {
            for (const key in obj) {
              if (Array.isArray(obj[key])) {
                obj[key] = obj[key].map(sanitizeValue);
              } else if (typeof obj[key] === 'object' && obj[key] !== null) {
                sanitizeObject(obj[key]);
              } else {
                obj[key] = sanitizeValue(obj[key]);
              }
            }
          }
        };

        sanitizeObject(req.body);
        sanitizeObject(req.query);

        next();
      } catch (error) {
        logger.error('Input sanitization failed', { error: error.message });
        res.status(400).json({
          error: 'Invalid input data',
          code: 'INVALID_INPUT'
        });
      }
    };
  }

  /**
   * Request size limiting middleware
   */
  getRequestSizeLimit() {
    return (req, res, next) => {
      const maxSize = 10 * 1024 * 1024; // 10MB
      const contentLength = parseInt(req.get('Content-Length') || '0');

      if (contentLength > maxSize) {
        logger.warn('Request size limit exceeded', {
          ip: req.ip,
          contentLength,
          maxSize,
          endpoint: req.originalUrl
        });

        return res.status(413).json({
          error: 'Request entity too large',
          code: 'REQUEST_TOO_LARGE',
          maxSize: maxSize
        });
      }

      next();
    };
  }

  /**
   * HTTP Parameter Pollution protection
   */
  getHppProtection() {
    return hpp({
      whitelist: ['tags', 'categories', 'sort'] // Allow arrays for these parameters
    });
  }

  /**
   * Suspicious activity detection
   */
  getSuspiciousActivityDetection() {
    const suspiciousPatterns = [
      /(\.\.|\/etc\/|\/proc\/|\/sys\/)/i, // Path traversal
      /(union|select|insert|update|delete|drop|create|alter)/i, // SQL injection
      /(<script|javascript:|vbscript:|onload|onerror)/i, // XSS attempts
      /(eval\(|setTimeout\(|setInterval\()/i, // Code injection
      /(\$\{|\#\{|<%=)/i // Template injection
    ];

    const checkSuspicious = (value) => {
      if (typeof value === 'string') {
        return suspiciousPatterns.some(pattern => pattern.test(value));
      }
      return false;
    };

    const checkObject = (obj) => {
      for (const key in obj) {
        if (checkSuspicious(obj[key]) || checkSuspicious(key)) {
          return true;
        }
        if (typeof obj[key] === 'object' && obj[key] !== null) {
          if (checkObject(obj[key])) {
            return true;
          }
        }
      }
      return false;
    };

    return (req, res, next) => {
      // Check URL, query params, and body
      if (checkSuspicious(req.url) || 
          checkObject(req.query) || 
          (req.body && checkObject(req.body))) {
        
        logger.warn('Suspicious activity detected', {
          ip: req.ip,
          endpoint: req.originalUrl,
          userAgent: req.get('User-Agent'),
          body: req.body,
          query: req.query,
          userId: req.user?._id
        });

        return res.status(400).json({
          error: 'Suspicious activity detected',
          code: 'SUSPICIOUS_ACTIVITY'
        });
      }

      next();
    };
  }

  /**
   * Get all security middleware in order
   */
  getAllMiddleware() {
    return [
      this.getSecurityHeaders(),
      this.getRequestSizeLimit(),
      this.getHppProtection(),
      this.getSanitizeInput(),
      this.getSuspiciousActivityDetection(),
      this.getRateLimit()
    ];
  }
}

module.exports = SecurityMiddleware;
