/// <reference types="node" />
import { CustomError } from '../../../../errors';
export declare function lookupLocal(iacCachePath: string, resourceName: string, userResourcePath: string | undefined, validResourceCondition: (path: string) => Promise<boolean>): Promise<string | undefined>;
export declare class InvalidUserPathError extends CustomError {
    constructor(message: string);
}
export declare function fetchCacheResource(url: string): Promise<Buffer>;
