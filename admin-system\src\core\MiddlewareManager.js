const express = require('express');
const cors = require('cors');
const compression = require('compression');
const cookieParser = require('cookie-parser');
const path = require('path');
const { logger } = require('../../utils/logger');

// Import optimized middleware classes
const SecurityMiddleware = require('../middleware/SecurityMiddleware');
const AuthMiddleware = require('../middleware/AuthMiddleware');
const ValidationMiddleware = require('../middleware/ValidationMiddleware');

// Import legacy middleware for compatibility
const { requestLogger } = require('../../middleware/security');
const { csrfProtection } = require('../../middleware/security');

/**
 * Middleware Manager
 * Handles all middleware setup and configuration
 */
class MiddlewareManager {
  constructor(app, config) {
    this.app = app;
    this.config = config;

    // Initialize middleware classes
    this.security = new SecurityMiddleware(config);
    this.auth = new AuthMiddleware(config);
    this.validation = new ValidationMiddleware();
  }

  /**
   * Setup all middleware in the correct order
   */
  async setup() {
    try {
      logger.info('Setting up middleware...');

      // Core middleware
      this.setupCoreMiddleware();
      
      // Security middleware
      this.setupSecurityMiddleware();
      
      // Static file middleware
      this.setupStaticFiles();
      
      // API middleware
      this.setupApiMiddleware();

      logger.info('Middleware setup completed successfully');
    } catch (error) {
      logger.error('Middleware setup failed', { error: error.message });
      throw error;
    }
  }

  /**
   * Setup core Express middleware
   */
  setupCoreMiddleware() {
    // Trust proxy (for accurate IP addresses behind load balancers)
    this.app.set('trust proxy', 1);

    // Request logging (should be early in the chain)
    this.app.use(requestLogger);

    // Compression
    this.app.use(compression({
      level: 6,
      threshold: 1024,
      filter: (req, res) => {
        if (req.headers['x-no-compression']) {
          return false;
        }
        return compression.filter(req, res);
      }
    }));

    // Body parsing
    this.app.use(express.json({ 
      limit: '10mb',
      verify: (req, res, buf) => {
        // Store raw body for webhook verification if needed
        req.rawBody = buf;
      }
    }));
    
    this.app.use(express.urlencoded({ 
      extended: true, 
      limit: '10mb' 
    }));

    // Cookie parsing
    this.app.use(cookieParser());
  }

  /**
   * Setup security middleware
   */
  setupSecurityMiddleware() {
    // Apply all security middleware in the correct order
    const securityMiddleware = this.security.getAllMiddleware();
    securityMiddleware.forEach(middleware => {
      this.app.use(middleware);
    });

    // CORS (after security headers)
    this.app.use(cors(this.getCorsConfig()));
  }

  /**
   * Setup static file serving
   */
  setupStaticFiles() {
    // Public website static files (read-only)
    this.app.use('/', express.static(path.join(__dirname, '../../public/website'), {
      maxAge: process.env.NODE_ENV === 'production' ? '1h' : 0,
      etag: true,
      lastModified: true,
      setHeaders: (res, filePath) => {
        // Security headers for static files
        res.setHeader('X-Content-Type-Options', 'nosniff');
        res.setHeader('X-Frame-Options', 'SAMEORIGIN');

        // Cache control for different file types
        if (filePath.endsWith('.html')) {
          res.setHeader('Cache-Control', 'no-cache');
        } else if (filePath.match(/\.(js|css)$/)) {
          res.setHeader('Cache-Control', 'public, max-age=3600');
        }
      }
    }));

    // Admin panel static files
    this.app.use('/admin', express.static(path.join(__dirname, '../../public/admin'), {
      maxAge: process.env.NODE_ENV === 'production' ? '1h' : 0,
      etag: true,
      lastModified: true,
      setHeaders: (res, filePath) => {
        res.setHeader('X-Content-Type-Options', 'nosniff');
        res.setHeader('X-Frame-Options', 'DENY');
        
        if (filePath.endsWith('.html')) {
          res.setHeader('Cache-Control', 'no-cache');
        }
      }
    }));

    // Dating profile static files
    this.app.use('/dating-profile', express.static(path.join(__dirname, '../../Dating-Resume-Profile/dating-resume-profile/src'), {
      maxAge: process.env.NODE_ENV === 'production' ? '30m' : 0,
      etag: true,
      lastModified: true
    }));
  }

  /**
   * Setup API-specific middleware
   */
  setupApiMiddleware() {
    // CSRF protection for API routes (after static files)
    this.app.use('/api', csrfProtection);
    
    // Block public editing attempts
    this.app.use('/api', blockPublicEditing);
  }

  /**
   * Get CORS configuration
   */
  getCorsConfig() {
    const origins = process.env.CORS_ORIGINS;
    
    return {
      origin: origins ? origins.split(',').map(origin => origin.trim()) : 
              (process.env.NODE_ENV === 'development' ? true : false),
      credentials: true,
      methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'],
      allowedHeaders: ['Content-Type', 'Authorization', 'X-CSRF-Token']
    };
  }

  /**
   * Setup admin-specific middleware for protected routes
   */
  setupAdminMiddleware() {
    return [
      this.auth.authenticate(),
      this.auth.validateSession(),
      this.auth.requireAdminAccess()
    ];
  }

  /**
   * Get middleware for different route types
   */
  getMiddleware(type) {
    switch (type) {
      case 'admin':
        return this.setupAdminMiddleware();
      case 'auth':
        return [this.security.getLoginRateLimit()];
      case 'api':
        return [this.security.getApiRateLimit()];
      case 'public':
        return [this.auth.optionalAuth()];
      default:
        return [];
    }
  }

  /**
   * Get validation middleware
   */
  getValidation(type) {
    switch (type) {
      case 'userCreation':
        return this.validation.validateUserCreation();
      case 'userUpdate':
        return this.validation.validateUserUpdate();
      case 'login':
        return this.validation.validateLogin();
      case 'contentCreation':
        return this.validation.validateContentCreation();
      case 'contentUpdate':
        return this.validation.validateContentUpdate();
      case 'datingProfile':
        return this.validation.validateDatingProfile();
      case 'search':
        return this.validation.validateSearch();
      case 'pagination':
        return this.validation.validatePagination();
      case 'id':
        return this.validation.validateId();
      default:
        return [];
    }
  }
}

module.exports = MiddlewareManager;
