import { IacFileInDirectory, IacOutputMeta, Options, TestOptions } from '../../../../lib/types';
import * as ora from 'ora';
import { IacOrgSettings } from './local-execution/types';
import { OciRegistry } from './local-execution/rules/oci-registry';
export declare function scan(iacOrgSettings: IacOrgSettings, options: any, testSpinner: ora.Ora | undefined, paths: string[], orgPublicId: string, buildOciRules: () => OciRegistry, projectRoot: string, remoteRepoUrl?: string, targetName?: string): Promise<{
    iacOutputMeta: IacOutputMeta;
    iacScanFailures: IacFileInDirectory[];
    iacIgnoredIssuesCount: number;
    results: any[];
    resultOptions: (Options & TestOptions)[];
}>;
