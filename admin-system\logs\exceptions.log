{"timestamp":"2025-08-25 01:18:59.850","level":"ERROR","message":"uncaughtException: Cannot read properties of undefined (reading 'collection')\nTypeError: Cannot read properties of undefined (reading 'collection')\n    at F:\\Website_Builds\\BlogWebsite01\\admin-system\\middleware\\security.js:77:32\n    at new module.exports (F:\\Website_Builds\\BlogWebsite01\\admin-system\\node_modules\\express-brute-mongo\\index.js:9:3)\n    at Object.<anonymous> (F:\\Website_Builds\\BlogWebsite01\\admin-system\\middleware\\security.js:76:25)\n    at Module._compile (node:internal/modules/cjs/loader:1730:14)\n    at Object..js (node:internal/modules/cjs/loader:1895:10)\n    at Module.load (node:internal/modules/cjs/loader:1465:32)\n    at Function._load (node:internal/modules/cjs/loader:1282:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:235:24)\n    at Module.require (node:internal/modules/cjs/loader:1487:12)","error":{},"stack":"TypeError: Cannot read properties of undefined (reading 'collection')\n    at F:\\Website_Builds\\BlogWebsite01\\admin-system\\middleware\\security.js:77:32\n    at new module.exports (F:\\Website_Builds\\BlogWebsite01\\admin-system\\node_modules\\express-brute-mongo\\index.js:9:3)\n    at Object.<anonymous> (F:\\Website_Builds\\BlogWebsite01\\admin-system\\middleware\\security.js:76:25)\n    at Module._compile (node:internal/modules/cjs/loader:1730:14)\n    at Object..js (node:internal/modules/cjs/loader:1895:10)\n    at Module.load (node:internal/modules/cjs/loader:1465:32)\n    at Function._load (node:internal/modules/cjs/loader:1282:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:235:24)\n    at Module.require (node:internal/modules/cjs/loader:1487:12)","exception":true,"date":"Mon Aug 25 2025 01:18:59 GMT-0700 (Mountain Standard Time)","process":{"pid":25976,"uid":null,"gid":null,"cwd":"F:\\Website_Builds\\BlogWebsite01\\admin-system","execPath":"C:\\Program Files\\nodejs\\node.exe","version":"v22.15.0","argv":["C:\\Program Files\\nodejs\\node.exe","F:\\Website_Builds\\BlogWebsite01\\admin-system\\server.js"],"memoryUsage":{"rss":79699968,"heapTotal":36044800,"heapUsed":26589336,"external":20515366,"arrayBuffers":18264303}},"os":{"loadavg":[0,0,0],"uptime":567109.093},"trace":[{"column":32,"file":"F:\\Website_Builds\\BlogWebsite01\\admin-system\\middleware\\security.js","function":null,"line":77,"method":null,"native":false},{"column":3,"file":"F:\\Website_Builds\\BlogWebsite01\\admin-system\\node_modules\\express-brute-mongo\\index.js","function":"new module.exports","line":9,"method":"exports","native":false},{"column":25,"file":"F:\\Website_Builds\\BlogWebsite01\\admin-system\\middleware\\security.js","function":null,"line":76,"method":null,"native":false},{"column":14,"file":"node:internal/modules/cjs/loader","function":"Module._compile","line":1730,"method":"_compile","native":false},{"column":10,"file":"node:internal/modules/cjs/loader","function":"Object..js","line":1895,"method":".js","native":false},{"column":32,"file":"node:internal/modules/cjs/loader","function":"Module.load","line":1465,"method":"load","native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Function._load","line":1282,"method":"_load","native":false},{"column":14,"file":"node:diagnostics_channel","function":"TracingChannel.traceSync","line":322,"method":"traceSync","native":false},{"column":24,"file":"node:internal/modules/cjs/loader","function":"wrapModuleLoad","line":235,"method":null,"native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Module.require","line":1487,"method":"require","native":false}],"service":"admin-panel","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-25 01:19:37.922","level":"ERROR","message":"uncaughtException: Cannot read properties of undefined (reading 'collection')\nTypeError: Cannot read properties of undefined (reading 'collection')\n    at F:\\Website_Builds\\BlogWebsite01\\admin-system\\middleware\\security.js:77:32\n    at new module.exports (F:\\Website_Builds\\BlogWebsite01\\admin-system\\node_modules\\express-brute-mongo\\index.js:9:3)\n    at Object.<anonymous> (F:\\Website_Builds\\BlogWebsite01\\admin-system\\middleware\\security.js:76:25)\n    at Module._compile (node:internal/modules/cjs/loader:1730:14)\n    at Object..js (node:internal/modules/cjs/loader:1895:10)\n    at Module.load (node:internal/modules/cjs/loader:1465:32)\n    at Function._load (node:internal/modules/cjs/loader:1282:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:235:24)\n    at Module.require (node:internal/modules/cjs/loader:1487:12)","error":{},"stack":"TypeError: Cannot read properties of undefined (reading 'collection')\n    at F:\\Website_Builds\\BlogWebsite01\\admin-system\\middleware\\security.js:77:32\n    at new module.exports (F:\\Website_Builds\\BlogWebsite01\\admin-system\\node_modules\\express-brute-mongo\\index.js:9:3)\n    at Object.<anonymous> (F:\\Website_Builds\\BlogWebsite01\\admin-system\\middleware\\security.js:76:25)\n    at Module._compile (node:internal/modules/cjs/loader:1730:14)\n    at Object..js (node:internal/modules/cjs/loader:1895:10)\n    at Module.load (node:internal/modules/cjs/loader:1465:32)\n    at Function._load (node:internal/modules/cjs/loader:1282:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:235:24)\n    at Module.require (node:internal/modules/cjs/loader:1487:12)","exception":true,"date":"Mon Aug 25 2025 01:19:37 GMT-0700 (Mountain Standard Time)","process":{"pid":23548,"uid":null,"gid":null,"cwd":"F:\\Website_Builds\\BlogWebsite01\\admin-system","execPath":"C:\\Program Files\\nodejs\\node.exe","version":"v22.15.0","argv":["C:\\Program Files\\nodejs\\node.exe","F:\\Website_Builds\\BlogWebsite01\\admin-system\\server.js"],"memoryUsage":{"rss":79601664,"heapTotal":36044800,"heapUsed":26831152,"external":20515366,"arrayBuffers":18264303}},"os":{"loadavg":[0,0,0],"uptime":567147.156},"trace":[{"column":32,"file":"F:\\Website_Builds\\BlogWebsite01\\admin-system\\middleware\\security.js","function":null,"line":77,"method":null,"native":false},{"column":3,"file":"F:\\Website_Builds\\BlogWebsite01\\admin-system\\node_modules\\express-brute-mongo\\index.js","function":"new module.exports","line":9,"method":"exports","native":false},{"column":25,"file":"F:\\Website_Builds\\BlogWebsite01\\admin-system\\middleware\\security.js","function":null,"line":76,"method":null,"native":false},{"column":14,"file":"node:internal/modules/cjs/loader","function":"Module._compile","line":1730,"method":"_compile","native":false},{"column":10,"file":"node:internal/modules/cjs/loader","function":"Object..js","line":1895,"method":".js","native":false},{"column":32,"file":"node:internal/modules/cjs/loader","function":"Module.load","line":1465,"method":"load","native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Function._load","line":1282,"method":"_load","native":false},{"column":14,"file":"node:diagnostics_channel","function":"TracingChannel.traceSync","line":322,"method":"traceSync","native":false},{"column":24,"file":"node:internal/modules/cjs/loader","function":"wrapModuleLoad","line":235,"method":null,"native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Module.require","line":1487,"method":"require","native":false}],"service":"admin-panel","version":"1.0.0","environment":"development"}
