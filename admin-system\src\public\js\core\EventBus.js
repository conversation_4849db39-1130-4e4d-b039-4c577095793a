/**
 * Event Bus
 * Centralized event management system for decoupled communication
 */
class EventBus {
  constructor() {
    this.events = new Map();
    this.onceEvents = new Map();
  }

  /**
   * Subscribe to an event
   */
  on(eventName, callback, context = null) {
    if (!this.events.has(eventName)) {
      this.events.set(eventName, []);
    }

    const listener = {
      callback,
      context,
      id: this.generateId()
    };

    this.events.get(eventName).push(listener);
    return listener.id;
  }

  /**
   * Subscribe to an event (one-time only)
   */
  once(eventName, callback, context = null) {
    if (!this.onceEvents.has(eventName)) {
      this.onceEvents.set(eventName, []);
    }

    const listener = {
      callback,
      context,
      id: this.generateId()
    };

    this.onceEvents.get(eventName).push(listener);
    return listener.id;
  }

  /**
   * Unsubscribe from an event
   */
  off(eventName, listenerId = null) {
    if (listenerId) {
      // Remove specific listener
      this.removeListener(this.events, eventName, listenerId);
      this.removeListener(this.onceEvents, eventName, listenerId);
    } else {
      // Remove all listeners for event
      this.events.delete(eventName);
      this.onceEvents.delete(eventName);
    }
  }

  /**
   * Emit an event
   */
  emit(eventName, ...args) {
    // Handle regular listeners
    if (this.events.has(eventName)) {
      const listeners = [...this.events.get(eventName)];
      listeners.forEach(listener => {
        try {
          if (listener.context) {
            listener.callback.apply(listener.context, args);
          } else {
            listener.callback(...args);
          }
        } catch (error) {
          console.error(`Error in event listener for '${eventName}':`, error);
        }
      });
    }

    // Handle one-time listeners
    if (this.onceEvents.has(eventName)) {
      const listeners = [...this.onceEvents.get(eventName)];
      this.onceEvents.delete(eventName); // Remove all once listeners

      listeners.forEach(listener => {
        try {
          if (listener.context) {
            listener.callback.apply(listener.context, args);
          } else {
            listener.callback(...args);
          }
        } catch (error) {
          console.error(`Error in once event listener for '${eventName}':`, error);
        }
      });
    }
  }

  /**
   * Remove specific listener from a collection
   */
  removeListener(collection, eventName, listenerId) {
    if (collection.has(eventName)) {
      const listeners = collection.get(eventName);
      const index = listeners.findIndex(l => l.id === listenerId);
      if (index !== -1) {
        listeners.splice(index, 1);
        if (listeners.length === 0) {
          collection.delete(eventName);
        }
      }
    }
  }

  /**
   * Generate unique listener ID
   */
  generateId() {
    return Math.random().toString(36).substr(2, 9);
  }

  /**
   * Get all event names
   */
  getEventNames() {
    const regularEvents = Array.from(this.events.keys());
    const onceEvents = Array.from(this.onceEvents.keys());
    return [...new Set([...regularEvents, ...onceEvents])];
  }

  /**
   * Get listener count for an event
   */
  getListenerCount(eventName) {
    const regularCount = this.events.has(eventName) ? this.events.get(eventName).length : 0;
    const onceCount = this.onceEvents.has(eventName) ? this.onceEvents.get(eventName).length : 0;
    return regularCount + onceCount;
  }

  /**
   * Clear all listeners
   */
  clear() {
    this.events.clear();
    this.onceEvents.clear();
  }

  /**
   * Debug information
   */
  debug() {
    console.group('EventBus Debug Info');
    console.log('Regular Events:', Object.fromEntries(
      Array.from(this.events.entries()).map(([name, listeners]) => [name, listeners.length])
    ));
    console.log('Once Events:', Object.fromEntries(
      Array.from(this.onceEvents.entries()).map(([name, listeners]) => [name, listeners.length])
    ));
    console.groupEnd();
  }
}

/**
 * Predefined event constants
 */
const Events = {
  // Authentication events
  USER_LOGIN: 'user:login',
  USER_LOGOUT: 'user:logout',
  USER_TOKEN_EXPIRED: 'user:token-expired',
  
  // UI events
  MODAL_OPEN: 'modal:open',
  MODAL_CLOSE: 'modal:close',
  NOTIFICATION_SHOW: 'notification:show',
  NOTIFICATION_HIDE: 'notification:hide',
  
  // Data events
  DATA_LOADED: 'data:loaded',
  DATA_UPDATED: 'data:updated',
  DATA_DELETED: 'data:deleted',
  DATA_ERROR: 'data:error',
  
  // Navigation events
  ROUTE_CHANGE: 'route:change',
  PAGE_LOAD: 'page:load',
  
  // Form events
  FORM_SUBMIT: 'form:submit',
  FORM_VALIDATE: 'form:validate',
  FORM_ERROR: 'form:error',
  
  // Admin events
  ADMIN_ACTION: 'admin:action',
  ADMIN_ERROR: 'admin:error',
  
  // System events
  SYSTEM_ERROR: 'system:error',
  SYSTEM_WARNING: 'system:warning',
  SYSTEM_INFO: 'system:info'
};

// Create singleton instance
const eventBus = new EventBus();

// Export for use in other modules
window.EventBus = EventBus;
window.Events = Events;
window.eventBus = eventBus;
