const { logger } = require('../../utils/logger');

/**
 * Standardized Response Handler
 * Provides consistent API response formats and error handling
 */
class ResponseHandler {
  constructor() {
    this.defaultMessages = {
      200: 'Success',
      201: 'Created successfully',
      204: 'No content',
      400: 'Bad request',
      401: 'Unauthorized',
      403: 'Forbidden',
      404: 'Not found',
      409: 'Conflict',
      422: 'Validation failed',
      429: 'Too many requests',
      500: 'Internal server error'
    };
  }

  /**
   * Send success response
   */
  success(res, data = null, message = null, statusCode = 200, meta = {}) {
    const response = {
      success: true,
      status: statusCode,
      message: message || this.defaultMessages[statusCode],
      timestamp: new Date().toISOString()
    };

    if (data !== null) {
      response.data = data;
    }

    if (Object.keys(meta).length > 0) {
      response.meta = meta;
    }

    return res.status(statusCode).json(response);
  }

  /**
   * Send error response
   */
  error(res, message = null, statusCode = 500, code = null, details = null) {
    const response = {
      success: false,
      status: statusCode,
      error: message || this.defaultMessages[statusCode],
      timestamp: new Date().toISOString()
    };

    if (code) {
      response.code = code;
    }

    if (details) {
      response.details = details;
    }

    return res.status(statusCode).json(response);
  }

  /**
   * Send validation error response
   */
  validationError(res, errors, message = 'Validation failed') {
    return this.error(res, message, 422, 'VALIDATION_ERROR', {
      validationErrors: errors
    });
  }

  /**
   * Send not found response
   */
  notFound(res, resource = 'Resource', message = null) {
    return this.error(
      res, 
      message || `${resource} not found`, 
      404, 
      'NOT_FOUND'
    );
  }

  /**
   * Send unauthorized response
   */
  unauthorized(res, message = 'Authentication required') {
    return this.error(res, message, 401, 'UNAUTHORIZED');
  }

  /**
   * Send forbidden response
   */
  forbidden(res, message = 'Access denied') {
    return this.error(res, message, 403, 'FORBIDDEN');
  }

  /**
   * Send conflict response
   */
  conflict(res, message = 'Resource already exists', field = null) {
    const details = field ? { conflictField: field } : null;
    return this.error(res, message, 409, 'CONFLICT', details);
  }

  /**
   * Send created response
   */
  created(res, data, message = 'Resource created successfully') {
    return this.success(res, data, message, 201);
  }

  /**
   * Send paginated response
   */
  paginated(res, data, pagination, message = 'Data retrieved successfully') {
    return this.success(res, data, message, 200, { pagination });
  }

  /**
   * Send no content response
   */
  noContent(res) {
    return res.status(204).send();
  }

  /**
   * Handle async route errors
   */
  asyncHandler(fn) {
    return (req, res, next) => {
      Promise.resolve(fn(req, res, next)).catch(next);
    };
  }

  /**
   * Log and send server error
   */
  serverError(res, error, context = {}, message = 'Internal server error') {
    logger.error('Server error', {
      error: error.message,
      stack: error.stack,
      ...context
    });

    return this.error(res, message, 500, 'SERVER_ERROR');
  }

  /**
   * Handle database errors
   */
  handleDatabaseError(res, error, context = {}) {
    if (error.name === 'ValidationError') {
      const validationErrors = Object.values(error.errors).map(err => ({
        field: err.path,
        message: err.message,
        value: err.value
      }));
      return this.validationError(res, validationErrors, 'Database validation failed');
    }

    if (error.name === 'CastError') {
      return this.error(res, 'Invalid ID format', 400, 'INVALID_ID');
    }

    if (error.code === 11000) {
      const field = Object.keys(error.keyPattern)[0];
      return this.conflict(res, `${field} already exists`, field);
    }

    return this.serverError(res, error, context);
  }

  /**
   * Create standardized pagination object
   */
  createPagination(page, limit, total, additionalData = {}) {
    const totalPages = Math.ceil(total / limit);
    
    return {
      page: parseInt(page),
      limit: parseInt(limit),
      total: parseInt(total),
      pages: totalPages,
      hasNext: page < totalPages,
      hasPrev: page > 1,
      ...additionalData
    };
  }

  /**
   * Validate pagination parameters
   */
  validatePagination(req) {
    const page = Math.max(1, parseInt(req.query.page) || 1);
    const limit = Math.min(100, Math.max(1, parseInt(req.query.limit) || 20));
    const skip = (page - 1) * limit;

    return { page, limit, skip };
  }

  /**
   * Create search query from request
   */
  createSearchQuery(req, searchFields = []) {
    const query = {};
    
    // Text search
    if (req.query.search && searchFields.length > 0) {
      query.$or = searchFields.map(field => ({
        [field]: { $regex: req.query.search, $options: 'i' }
      }));
    }

    // Status filter
    if (req.query.status) {
      query.status = req.query.status;
    }

    // Date range filter
    if (req.query.startDate || req.query.endDate) {
      query.createdAt = {};
      if (req.query.startDate) {
        query.createdAt.$gte = new Date(req.query.startDate);
      }
      if (req.query.endDate) {
        query.createdAt.$lte = new Date(req.query.endDate);
      }
    }

    return query;
  }

  /**
   * Create sort object from request
   */
  createSort(req, defaultSort = { createdAt: -1 }) {
    if (!req.query.sort) {
      return defaultSort;
    }

    const sortField = req.query.sort;
    const sortOrder = req.query.order === 'asc' ? 1 : -1;
    
    return { [sortField]: sortOrder };
  }

  /**
   * Sanitize data for response
   */
  sanitizeData(data, fieldsToRemove = ['password', '__v']) {
    if (!data) return data;

    if (Array.isArray(data)) {
      return data.map(item => this.sanitizeData(item, fieldsToRemove));
    }

    if (typeof data === 'object' && data !== null) {
      const sanitized = { ...data };
      
      // Remove sensitive fields
      fieldsToRemove.forEach(field => {
        delete sanitized[field];
      });

      // Handle nested objects
      Object.keys(sanitized).forEach(key => {
        if (typeof sanitized[key] === 'object' && sanitized[key] !== null) {
          sanitized[key] = this.sanitizeData(sanitized[key], fieldsToRemove);
        }
      });

      return sanitized;
    }

    return data;
  }

  /**
   * Create audit log entry
   */
  createAuditLog(req, action, resource, targetId = null, changes = null) {
    return {
      action,
      resource,
      targetId,
      changes,
      userId: req.user?._id,
      username: req.user?.username,
      ip: req.ip,
      userAgent: req.get('User-Agent'),
      timestamp: new Date()
    };
  }
}

// Create singleton instance
const responseHandler = new ResponseHandler();

module.exports = responseHandler;
