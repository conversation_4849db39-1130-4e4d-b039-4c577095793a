const BaseController = require('./BaseController');
const User = require('../../models/User');
const responseHandler = require('../utils/ResponseHandler');
const { logger } = require('../../utils/logger');

/**
 * User Controller
 * Handles user management operations
 */
class UserController extends BaseController {
  constructor() {
    super(User, 'User');
  }

  /**
   * Get fields to search in
   */
  getSearchFields() {
    return ['username', 'email', 'firstName', 'lastName'];
  }

  /**
   * Get fields to populate
   */
  getPopulateFields() {
    return 'department';
  }

  /**
   * Apply additional filters to query
   */
  applyFilters(query, req) {
    // Filter by role
    if (req.query.role) {
      query.role = req.query.role;
    }

    // Filter by status
    if (req.query.status) {
      query.status = req.query.status;
    }

    // Filter by department
    if (req.query.department) {
      query.department = req.query.department;
    }

    // Exclude deleted users by default
    if (!req.query.includeDeleted) {
      query.deletedAt = { $exists: false };
    }
  }

  /**
   * Check if user can access resource
   */
  canAccess(resource, user, action) {
    // Super admins can access everything
    if (user.role === 'super_admin') {
      return true;
    }

    // Admins can access non-super-admin users
    if (user.role === 'admin' && resource.role !== 'super_admin') {
      return true;
    }

    // Users can only access their own profile for read/update
    if (action === 'read' || action === 'update') {
      return resource._id.toString() === user._id.toString();
    }

    return false;
  }

  /**
   * Prepare data for creation
   */
  prepareCreateData(data, user) {
    const userData = super.prepareCreateData(data, user);
    
    // Set default values
    userData.status = userData.status || 'active';
    userData.profile = userData.profile || {};
    userData.security = {
      activeSessions: [],
      loginAttempts: 0,
      lockUntil: null,
      passwordChangedAt: new Date()
    };
    userData.mfa = {
      enabled: false,
      secret: null,
      backupCodes: []
    };

    return userData;
  }

  /**
   * Prepare data for update
   */
  prepareUpdateData(data, user, existingResource) {
    const updateData = super.prepareUpdateData(data, user, existingResource);
    
    // Don't allow role changes unless user is super_admin
    if (user.role !== 'super_admin') {
      delete updateData.role;
    }

    // Don't allow status changes unless user is admin or super_admin
    if (!['admin', 'super_admin'].includes(user.role)) {
      delete updateData.status;
    }

    // Handle password updates
    if (updateData.password) {
      updateData.security = {
        ...existingResource.security,
        passwordChangedAt: new Date()
      };
    }

    return updateData;
  }

  /**
   * Validate creation
   */
  async validateCreate(data, user) {
    // Check if user already exists
    const existingUser = await User.findOne({
      $or: [
        { email: data.email },
        { username: data.username }
      ]
    });

    if (existingUser) {
      const field = existingUser.email === data.email ? 'email' : 'username';
      return {
        message: `User with this ${field} already exists`,
        code: 'USER_EXISTS'
      };
    }

    // Check role permissions
    if (data.role === 'super_admin' && user.role !== 'super_admin') {
      return {
        message: 'Only super admins can create super admin users',
        code: 'INSUFFICIENT_PERMISSIONS'
      };
    }

    return null;
  }

  /**
   * Validate update
   */
  async validateUpdate(data, user, existingResource) {
    // Check if email/username already exists (excluding current user)
    if (data.email || data.username) {
      const query = {
        _id: { $ne: existingResource._id },
        $or: []
      };

      if (data.email) {
        query.$or.push({ email: data.email });
      }
      if (data.username) {
        query.$or.push({ username: data.username });
      }

      const conflictingUser = await User.findOne(query);
      if (conflictingUser) {
        const field = conflictingUser.email === data.email ? 'email' : 'username';
        return {
          message: `Another user already has this ${field}`,
          code: 'USER_EXISTS'
        };
      }
    }

    // Check role change permissions
    if (data.role && data.role !== existingResource.role) {
      if (user.role !== 'super_admin') {
        return {
          message: 'Only super admins can change user roles',
          code: 'INSUFFICIENT_PERMISSIONS'
        };
      }

      if (data.role === 'super_admin' && user._id.toString() !== existingResource._id.toString()) {
        return {
          message: 'Cannot promote other users to super admin',
          code: 'INSUFFICIENT_PERMISSIONS'
        };
      }
    }

    return null;
  }

  /**
   * Validate deletion
   */
  async validateDelete(resource, user) {
    // Cannot delete super admin users
    if (resource.role === 'super_admin') {
      return {
        message: 'Cannot delete super admin users',
        code: 'CANNOT_DELETE_SUPER_ADMIN'
      };
    }

    // Cannot delete yourself
    if (resource._id.toString() === user._id.toString()) {
      return {
        message: 'Cannot delete your own account',
        code: 'CANNOT_DELETE_SELF'
      };
    }

    return null;
  }

  /**
   * Check if model supports soft delete
   */
  supportsSoftDelete() {
    return true;
  }

  /**
   * Get user statistics
   */
  getStats = responseHandler.asyncHandler(async (req, res) => {
    try {
      const [total, active, inactive, locked, newThisMonth] = await Promise.all([
        User.countDocuments({ deletedAt: { $exists: false } }),
        User.countDocuments({ status: 'active', deletedAt: { $exists: false } }),
        User.countDocuments({ status: 'inactive', deletedAt: { $exists: false } }),
        User.countDocuments({ 
          'security.lockUntil': { $gt: new Date() },
          deletedAt: { $exists: false }
        }),
        User.countDocuments({
          createdAt: { 
            $gte: new Date(new Date().getFullYear(), new Date().getMonth(), 1) 
          },
          deletedAt: { $exists: false }
        })
      ]);

      const stats = {
        total,
        active,
        inactive,
        locked,
        newThisMonth
      };

      return responseHandler.success(res, stats, 'User statistics retrieved successfully');
    } catch (error) {
      return responseHandler.serverError(res, error, {
        action: 'getStats',
        userId: req.user._id
      });
    }
  });

  /**
   * Change user password
   */
  changePassword = responseHandler.asyncHandler(async (req, res) => {
    try {
      const { currentPassword, newPassword } = req.body;
      const userId = req.params.id || req.user._id;

      const user = await User.findById(userId).select('+password');
      if (!user) {
        return responseHandler.notFound(res, 'User');
      }

      // Check permissions
      if (!this.canAccess(user, req.user, 'update')) {
        return responseHandler.forbidden(res);
      }

      // Verify current password (unless admin changing another user's password)
      if (userId === req.user._id.toString()) {
        const isCurrentPasswordValid = await user.comparePassword(currentPassword);
        if (!isCurrentPasswordValid) {
          return responseHandler.error(res, 'Current password is incorrect', 400, 'INVALID_PASSWORD');
        }
      }

      // Update password
      user.password = newPassword;
      user.security.passwordChangedAt = new Date();
      await user.save();

      // Log password change
      this.logAction(req, 'password_change', userId);

      return responseHandler.success(res, null, 'Password changed successfully');
    } catch (error) {
      return responseHandler.handleDatabaseError(res, error, {
        action: 'changePassword',
        userId: req.user._id
      });
    }
  });

  /**
   * Toggle user status
   */
  toggleStatus = responseHandler.asyncHandler(async (req, res) => {
    try {
      const user = await User.findById(req.params.id);
      if (!user) {
        return responseHandler.notFound(res, 'User');
      }

      // Check permissions
      if (!this.canAccess(user, req.user, 'update')) {
        return responseHandler.forbidden(res);
      }

      // Toggle status
      const newStatus = user.status === 'active' ? 'inactive' : 'active';
      user.status = newStatus;
      user.updatedBy = req.user._id;
      user.updatedAt = new Date();
      await user.save();

      // Log status change
      this.logAction(req, 'status_change', user._id, { status: newStatus });

      return responseHandler.success(res, { status: newStatus }, 'User status updated successfully');
    } catch (error) {
      return responseHandler.handleDatabaseError(res, error, {
        action: 'toggleStatus',
        userId: req.user._id
      });
    }
  });
}

module.exports = new UserController();
