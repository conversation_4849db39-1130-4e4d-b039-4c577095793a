const { logger, securityLogger, performanceLogger } = require('../utils/logger');
const AuditLog = require('../models/AuditLog');
const User = require('../models/User');
const emailService = require('./emailService');

/**
 * Enterprise Monitoring Service
 * Real-time security monitoring, alerting, and performance tracking
 */

class MonitoringService {
  constructor() {
    this.alertThresholds = {
      failedLogins: 10, // per hour
      suspiciousActivity: 5, // per hour
      slowQueries: 100, // per hour
      errorRate: 0.05, // 5% error rate
      memoryUsage: 0.85 // 85% memory usage
    };

    this.alertCooldowns = new Map(); // Prevent spam alerts
    this.metrics = {
      requests: 0,
      errors: 0,
      slowQueries: 0,
      securityEvents: 0
    };

    this.init();
  }

  init() {
    // Start monitoring intervals
    this.startSecurityMonitoring();
    this.startPerformanceMonitoring();
    this.startHealthChecks();
    
    logger.info('Monitoring service initialized');
  }

  startSecurityMonitoring() {
    // Check for security threats every 5 minutes
    setInterval(async () => {
      try {
        await this.checkFailedLogins();
        await this.checkSuspiciousActivity();
        await this.checkAccountLockouts();
        await this.checkPrivilegeEscalations();
      } catch (error) {
        logger.error('Security monitoring error', { error: error.message });
      }
    }, 5 * 60 * 1000); // 5 minutes
  }

  startPerformanceMonitoring() {
    // Performance checks every minute
    setInterval(async () => {
      try {
        await this.checkSystemHealth();
        await this.checkDatabasePerformance();
        this.checkMemoryUsage();
      } catch (error) {
        logger.error('Performance monitoring error', { error: error.message });
      }
    }, 60 * 1000); // 1 minute
  }

  startHealthChecks() {
    // Health checks every 30 seconds
    setInterval(async () => {
      try {
        await this.performHealthCheck();
      } catch (error) {
        logger.error('Health check error', { error: error.message });
      }
    }, 30 * 1000); // 30 seconds
  }

  async checkFailedLogins() {
    const oneHourAgo = new Date(Date.now() - 60 * 60 * 1000);
    
    const failedLogins = await AuditLog.aggregate([
      {
        $match: {
          eventType: 'login_failed',
          timestamp: { $gte: oneHourAgo }
        }
      },
      {
        $group: {
          _id: '$ipAddress',
          count: { $sum: 1 },
          lastAttempt: { $max: '$timestamp' },
          usernames: { $addToSet: '$username' }
        }
      },
      {
        $match: {
          count: { $gte: this.alertThresholds.failedLogins }
        }
      }
    ]);

    for (const attack of failedLogins) {
      await this.triggerSecurityAlert('brute_force_attack', {
        ipAddress: attack._id,
        attempts: attack.count,
        timeframe: '1 hour',
        targetUsernames: attack.usernames,
        lastAttempt: attack.lastAttempt,
        severity: 'high'
      });
    }
  }

  async checkSuspiciousActivity() {
    const oneHourAgo = new Date(Date.now() - 60 * 60 * 1000);
    
    const suspiciousEvents = await AuditLog.countDocuments({
      eventType: 'suspicious_activity',
      timestamp: { $gte: oneHourAgo }
    });

    if (suspiciousEvents >= this.alertThresholds.suspiciousActivity) {
      await this.triggerSecurityAlert('high_suspicious_activity', {
        count: suspiciousEvents,
        timeframe: '1 hour',
        threshold: this.alertThresholds.suspiciousActivity,
        severity: 'high'
      });
    }
  }

  async checkAccountLockouts() {
    const lockedAccounts = await User.countDocuments({
      'security.lockUntil': { $gt: new Date() }
    });

    if (lockedAccounts > 5) { // More than 5 locked accounts
      await this.triggerSecurityAlert('mass_account_lockout', {
        lockedAccounts,
        severity: 'critical'
      });
    }
  }

  async checkPrivilegeEscalations() {
    const oneHourAgo = new Date(Date.now() - 60 * 60 * 1000);
    
    const escalations = await AuditLog.find({
      eventType: 'privilege_escalation',
      timestamp: { $gte: oneHourAgo }
    }).populate('userId', 'username email');

    if (escalations.length > 0) {
      await this.triggerSecurityAlert('privilege_escalation_detected', {
        count: escalations.length,
        escalations: escalations.map(e => ({
          user: e.userId?.username,
          timestamp: e.timestamp,
          details: e.metadata
        })),
        severity: 'high'
      });
    }
  }

  async checkSystemHealth() {
    const health = {
      timestamp: new Date(),
      uptime: process.uptime(),
      memory: process.memoryUsage(),
      cpu: process.cpuUsage(),
      activeConnections: 0, // Would be populated from actual metrics
      errorRate: this.metrics.errors / Math.max(this.metrics.requests, 1)
    };

    // Check error rate
    if (health.errorRate > this.alertThresholds.errorRate) {
      await this.triggerSystemAlert('high_error_rate', {
        errorRate: health.errorRate,
        threshold: this.alertThresholds.errorRate,
        requests: this.metrics.requests,
        errors: this.metrics.errors,
        severity: 'high'
      });
    }

    // Log health metrics
    performanceLogger.memoryUsage(health.memory);
    
    // Reset metrics
    this.metrics.requests = 0;
    this.metrics.errors = 0;
  }

  async checkDatabasePerformance() {
    // This would integrate with MongoDB monitoring
    // For now, we'll simulate slow query detection
    if (this.metrics.slowQueries > this.alertThresholds.slowQueries) {
      await this.triggerSystemAlert('high_slow_queries', {
        slowQueries: this.metrics.slowQueries,
        threshold: this.alertThresholds.slowQueries,
        timeframe: '1 hour',
        severity: 'medium'
      });
      
      this.metrics.slowQueries = 0;
    }
  }

  checkMemoryUsage() {
    const memUsage = process.memoryUsage();
    const totalMemory = memUsage.heapTotal;
    const usedMemory = memUsage.heapUsed;
    const memoryUsageRatio = usedMemory / totalMemory;

    if (memoryUsageRatio > this.alertThresholds.memoryUsage) {
      this.triggerSystemAlert('high_memory_usage', {
        memoryUsageRatio,
        threshold: this.alertThresholds.memoryUsage,
        usedMemory: Math.round(usedMemory / 1024 / 1024) + ' MB',
        totalMemory: Math.round(totalMemory / 1024 / 1024) + ' MB',
        severity: 'medium'
      });
    }
  }

  async performHealthCheck() {
    const health = {
      status: 'healthy',
      timestamp: new Date(),
      checks: {
        database: 'unknown',
        memory: 'unknown',
        disk: 'unknown',
        external_services: 'unknown'
      }
    };

    try {
      // Database check
      const mongoose = require('mongoose');
      health.checks.database = mongoose.connection.readyState === 1 ? 'healthy' : 'unhealthy';

      // Memory check
      const memUsage = process.memoryUsage();
      const memoryUsageRatio = memUsage.heapUsed / memUsage.heapTotal;
      health.checks.memory = memoryUsageRatio < 0.9 ? 'healthy' : 'warning';

      // Overall status
      const unhealthyChecks = Object.values(health.checks).filter(status => 
        status === 'unhealthy' || status === 'critical'
      );

      if (unhealthyChecks.length > 0) {
        health.status = 'unhealthy';
        await this.triggerSystemAlert('system_unhealthy', {
          checks: health.checks,
          severity: 'critical'
        });
      }

    } catch (error) {
      health.status = 'error';
      health.error = error.message;
      logger.error('Health check failed', { error: error.message });
    }

    return health;
  }

  async triggerSecurityAlert(alertType, details) {
    const alertKey = `${alertType}_${details.ipAddress || 'system'}`;
    
    // Check cooldown to prevent spam
    if (this.isInCooldown(alertKey)) {
      return;
    }

    // Log security alert
    securityLogger.securityViolation('system', alertType, details);

    // Create audit log entry
    await AuditLog.logEvent({
      eventType: 'security_alert',
      userId: null,
      username: 'system',
      userRole: 'system',
      targetType: 'system',
      action: 'alert',
      resource: 'security',
      description: `Security alert triggered: ${alertType}`,
      metadata: details,
      ipAddress: details.ipAddress || '127.0.0.1',
      severity: details.severity || 'medium'
    });

    // Send email alerts to administrators
    const adminEmails = await this.getAdminEmails();
    if (adminEmails.length > 0) {
      try {
        await emailService.sendSystemAlert(adminEmails, alertType, details);
      } catch (error) {
        logger.error('Failed to send security alert email', { error: error.message });
      }
    }

    // Set cooldown
    this.setCooldown(alertKey, 15 * 60 * 1000); // 15 minutes
  }

  async triggerSystemAlert(alertType, details) {
    const alertKey = `system_${alertType}`;
    
    // Check cooldown
    if (this.isInCooldown(alertKey)) {
      return;
    }

    // Log system alert
    logger.warn('System alert triggered', { alertType, details });

    // Create audit log entry
    await AuditLog.logEvent({
      eventType: 'system_alert',
      userId: null,
      username: 'system',
      userRole: 'system',
      targetType: 'system',
      action: 'alert',
      resource: 'system',
      description: `System alert triggered: ${alertType}`,
      metadata: details,
      ipAddress: '127.0.0.1',
      severity: details.severity || 'medium'
    });

    // Send email alerts for critical issues
    if (details.severity === 'critical' || details.severity === 'high') {
      const adminEmails = await this.getAdminEmails();
      if (adminEmails.length > 0) {
        try {
          await emailService.sendSystemAlert(adminEmails, alertType, details);
        } catch (error) {
          logger.error('Failed to send system alert email', { error: error.message });
        }
      }
    }

    // Set cooldown
    this.setCooldown(alertKey, 30 * 60 * 1000); // 30 minutes
  }

  async getAdminEmails() {
    try {
      const admins = await User.find({
        role: { $in: ['super_admin', 'admin'] },
        status: 'active',
        'profile.notifications.security': true
      }).select('email');

      return admins.map(admin => admin.email);
    } catch (error) {
      logger.error('Failed to get admin emails', { error: error.message });
      return [];
    }
  }

  isInCooldown(alertKey) {
    const cooldownEnd = this.alertCooldowns.get(alertKey);
    return cooldownEnd && Date.now() < cooldownEnd;
  }

  setCooldown(alertKey, duration) {
    this.alertCooldowns.set(alertKey, Date.now() + duration);
  }

  // Metrics tracking methods
  trackRequest() {
    this.metrics.requests++;
  }

  trackError() {
    this.metrics.errors++;
  }

  trackSlowQuery() {
    this.metrics.slowQueries++;
  }

  trackSecurityEvent() {
    this.metrics.securityEvents++;
  }

  // Get current metrics
  getMetrics() {
    return {
      ...this.metrics,
      uptime: process.uptime(),
      memory: process.memoryUsage(),
      timestamp: new Date()
    };
  }
}

// Create singleton instance
const monitoringService = new MonitoringService();

module.exports = monitoringService;
