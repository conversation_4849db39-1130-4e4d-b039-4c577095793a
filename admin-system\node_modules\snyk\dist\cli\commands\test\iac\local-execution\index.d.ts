import { IacFileParsed, IacFileParseFailure, IacOrgSettings, IaCTestFlags, RulesOrigin, SafeAnalyticsOutput, TestReturnValue } from './types';
import { ResultsProcessor } from './process-results';
import { CustomError } from '../../../../../lib/errors';
import { Tag } from '../../../../../lib/types';
export declare function test(resultsProcessor: ResultsProcessor, pathToScan: string, options: IaCTestFlags, iacOrgSettings: IacOrgSettings, rulesOrigin: RulesOrigin): Promise<TestReturnValue>;
export declare function removeFileContent({ filePath, fileType, failureReason, projectType, }: IacFileParsed | IacFileParseFailure): SafeAnalyticsOutput;
export declare function parseTags(options: IaCTestFlags): Tag[] | undefined;
export declare class InvalidVarFilePath extends CustomError {
    constructor(path: string, message?: string);
}
