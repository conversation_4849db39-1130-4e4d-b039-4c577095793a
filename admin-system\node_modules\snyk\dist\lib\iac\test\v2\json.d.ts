import { TestOutput } from './scan/results';
import { IacProjectType } from '../../constants';
import { State } from './scan/policy-engine';
import { IacTestError } from '../../../snyk-test/iac-test-result';
export interface Result {
    meta: Meta;
    filesystemPolicy: false;
    vulnerabilities: [];
    dependencyCount: 0;
    licensesPolicy: null;
    ignoreSettings: IgnoreSettings;
    targetFile: string;
    projectName: string;
    org: string;
    policy: string;
    isPrivate: boolean;
    targetFilePath: string;
    packageManager: IacProjectType | State.InputTypeEnum;
    path: string;
    projectType: IacProjectType | State.InputTypeEnum;
    ok: boolean;
    infrastructureAsCodeIssues: IacIssue[];
    infrastructureAsCodeSuccesses?: IacSuccess[];
    error?: string;
}
export interface IgnoreSettings {
    adminOnly: boolean;
    reasonRequired: boolean;
    disregardFilesystemIgnores: boolean;
}
export interface Meta {
    isPrivate: boolean;
    isLicensesEnabled: boolean;
    ignoreSettings: IgnoreSettings;
    org: string;
    policy: string;
}
export interface IgnoreSettings {
    adminOnly: boolean;
    reasonRequired: boolean;
    disregardFilesystemIgnores: boolean;
}
export interface IacIssue {
    severity: string;
    resolve: string;
    impact: string;
    msg: string;
    remediation?: Remediation;
    subType: string;
    issue: string;
    publicId: string;
    title: string;
    references: string[];
    id: string;
    isIgnored: boolean;
    iacDescription: IacDescription;
    lineNumber: number;
    documentation?: string;
    isGeneratedByCustomRule: boolean;
    path: string[];
    policyEngineType?: string;
    type?: IacProjectType | State.InputTypeEnum;
    compliance?: string[][];
    description: string;
}
export interface IacSuccess {
    id: string;
    severity: string;
    type: IacProjectType | State.InputTypeEnum;
    subType: string;
    path: string[];
    msg: string;
    isIgnored: boolean;
    evidence?: string;
}
export interface Remediation {
    cloudformation?: string;
    terraform?: string;
    arm?: string;
    kubernetes?: string;
}
export interface IacDescription {
    issue: string;
    impact: string;
    resolve: string;
}
export declare function convertEngineToJsonResults({ results, projectName, }: {
    results: TestOutput;
    projectName: string;
}): Array<Result | IacTestError>;
