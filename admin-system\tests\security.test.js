const request = require('supertest');
const mongoose = require('mongoose');
const app = require('../server');
const User = require('../models/User');
const { securityUtils } = require('../config/security');

/**
 * Security Test Suite
 * Tests for OWASP Top 10 and enterprise security requirements
 */

describe('Security Tests', () => {
  let server;
  let adminToken;
  let testUser;

  beforeAll(async () => {
    // Connect to test database
    await mongoose.connect(process.env.MONGODB_TEST_URI);
    
    // Start server
    server = app.listen(0);
    
    // Create test admin user
    const adminUser = new User({
      username: 'testadmin',
      email: '<EMAIL>',
      firstName: 'Test',
      lastName: 'Admin',
      role: 'super_admin',
      password: 'TestPassword123!',
      status: 'active'
    });
    await adminUser.save();
    
    // Get admin token
    const loginResponse = await request(server)
      .post('/api/auth/login')
      .send({
        email: '<EMAIL>',
        password: 'TestPassword123!'
      });
    
    adminToken = loginResponse.body.accessToken;
  });

  afterAll(async () => {
    // Clean up
    await User.deleteMany({});
    await mongoose.connection.close();
    server.close();
  });

  describe('Authentication Security', () => {
    test('should reject weak passwords', async () => {
      const weakPasswords = [
        'password',
        '123456',
        'Password',
        'password123',
        'Password1'
      ];

      for (const password of weakPasswords) {
        const validation = securityUtils.validatePasswordStrength(password);
        expect(validation.isValid).toBe(false);
        expect(validation.errors.length).toBeGreaterThan(0);
      }
    });

    test('should accept strong passwords', async () => {
      const strongPassword = 'SecurePassword123!@#';
      const validation = securityUtils.validatePasswordStrength(strongPassword);
      expect(validation.isValid).toBe(true);
      expect(validation.errors.length).toBe(0);
    });

    test('should hash passwords securely', async () => {
      const password = 'TestPassword123!';
      const hash = await securityUtils.hashPassword(password);
      
      expect(hash).not.toBe(password);
      expect(hash.length).toBeGreaterThan(50);
      expect(await securityUtils.verifyPassword(password, hash)).toBe(true);
    });

    test('should implement rate limiting on login', async () => {
      const loginAttempts = [];
      
      // Make multiple rapid login attempts
      for (let i = 0; i < 10; i++) {
        loginAttempts.push(
          request(server)
            .post('/api/auth/login')
            .send({
              email: '<EMAIL>',
              password: 'wrongpassword'
            })
        );
      }
      
      const responses = await Promise.all(loginAttempts);
      const rateLimitedResponses = responses.filter(res => res.status === 429);
      
      expect(rateLimitedResponses.length).toBeGreaterThan(0);
    });

    test('should invalidate tokens on logout', async () => {
      // Login
      const loginResponse = await request(server)
        .post('/api/auth/login')
        .send({
          email: '<EMAIL>',
          password: 'TestPassword123!'
        });
      
      const token = loginResponse.body.accessToken;
      
      // Verify token works
      const authResponse = await request(server)
        .get('/api/auth/me')
        .set('Authorization', `Bearer ${token}`);
      
      expect(authResponse.status).toBe(200);
      
      // Logout
      await request(server)
        .post('/api/auth/logout')
        .set('Authorization', `Bearer ${token}`);
      
      // Verify token is invalidated
      const invalidResponse = await request(server)
        .get('/api/auth/me')
        .set('Authorization', `Bearer ${token}`);
      
      expect(invalidResponse.status).toBe(401);
    });
  });

  describe('Input Validation and Sanitization', () => {
    test('should sanitize XSS attempts', async () => {
      const xssPayloads = [
        '<script>alert("xss")</script>',
        'javascript:alert("xss")',
        '<img src="x" onerror="alert(1)">',
        '"><script>alert("xss")</script>'
      ];

      for (const payload of xssPayloads) {
        const sanitized = securityUtils.sanitizeInput(payload);
        expect(sanitized).not.toContain('<script>');
        expect(sanitized).not.toContain('javascript:');
        expect(sanitized).not.toContain('onerror=');
      }
    });

    test('should prevent SQL injection attempts', async () => {
      const sqlPayloads = [
        "'; DROP TABLE users; --",
        "' OR '1'='1",
        "' UNION SELECT * FROM users --",
        "admin'--"
      ];

      for (const payload of sqlPayloads) {
        const response = await request(server)
          .get('/api/admin/users')
          .query({ search: payload })
          .set('Authorization', `Bearer ${adminToken}`);
        
        // Should not cause server error
        expect(response.status).not.toBe(500);
      }
    });

    test('should validate email formats', async () => {
      const invalidEmails = [
        'invalid-email',
        '@domain.com',
        'user@',
        '<EMAIL>',
        'user@domain',
        ''
      ];

      for (const email of invalidEmails) {
        const response = await request(server)
          .post('/api/admin/users')
          .send({
            username: 'testuser',
            email: email,
            firstName: 'Test',
            lastName: 'User',
            role: 'viewer'
          })
          .set('Authorization', `Bearer ${adminToken}`);
        
        expect(response.status).toBe(400);
      }
    });

    test('should reject oversized requests', async () => {
      const largePayload = 'x'.repeat(20 * 1024 * 1024); // 20MB
      
      const response = await request(server)
        .post('/api/admin/users')
        .send({
          username: 'testuser',
          email: '<EMAIL>',
          firstName: largePayload,
          lastName: 'User',
          role: 'viewer'
        })
        .set('Authorization', `Bearer ${adminToken}`);
      
      expect(response.status).toBe(413);
    });
  });

  describe('Authorization and Access Control', () => {
    test('should enforce role-based access control', async () => {
      // Create viewer user
      const viewerUser = new User({
        username: 'viewer',
        email: '<EMAIL>',
        firstName: 'Test',
        lastName: 'Viewer',
        role: 'viewer',
        password: 'TestPassword123!',
        status: 'active'
      });
      await viewerUser.save();

      // Login as viewer
      const loginResponse = await request(server)
        .post('/api/auth/login')
        .send({
          email: '<EMAIL>',
          password: 'TestPassword123!'
        });
      
      const viewerToken = loginResponse.body.accessToken;

      // Try to create user (should fail)
      const createResponse = await request(server)
        .post('/api/admin/users')
        .send({
          username: 'newuser',
          email: '<EMAIL>',
          firstName: 'New',
          lastName: 'User',
          role: 'viewer'
        })
        .set('Authorization', `Bearer ${viewerToken}`);
      
      expect(createResponse.status).toBe(403);
    });

    test('should require authentication for protected routes', async () => {
      const protectedRoutes = [
        { method: 'get', path: '/api/admin/users' },
        { method: 'post', path: '/api/admin/users' },
        { method: 'get', path: '/api/admin/stats/users' },
        { method: 'get', path: '/api/auth/me' }
      ];

      for (const route of protectedRoutes) {
        const response = await request(server)[route.method](route.path);
        expect(response.status).toBe(401);
      }
    });

    test('should validate JWT tokens properly', async () => {
      const invalidTokens = [
        'invalid-token',
        'Bearer invalid-token',
        'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.invalid.signature',
        ''
      ];

      for (const token of invalidTokens) {
        const response = await request(server)
          .get('/api/auth/me')
          .set('Authorization', token);
        
        expect(response.status).toBe(401);
      }
    });
  });

  describe('Security Headers', () => {
    test('should include security headers', async () => {
      const response = await request(server).get('/health');
      
      expect(response.headers['x-frame-options']).toBe('DENY');
      expect(response.headers['x-content-type-options']).toBe('nosniff');
      expect(response.headers['x-xss-protection']).toBe('1; mode=block');
      expect(response.headers['strict-transport-security']).toContain('max-age=');
      expect(response.headers['content-security-policy']).toBeDefined();
    });

    test('should not expose sensitive information', async () => {
      const response = await request(server).get('/health');
      
      expect(response.headers['server']).toBeUndefined();
      expect(response.headers['x-powered-by']).toBeUndefined();
    });
  });

  describe('Session Security', () => {
    test('should use secure session configuration', async () => {
      const response = await request(server)
        .post('/api/auth/login')
        .send({
          email: '<EMAIL>',
          password: 'TestPassword123!'
        });
      
      const cookies = response.headers['set-cookie'];
      if (cookies) {
        const sessionCookie = cookies.find(cookie => cookie.includes('admin.sid'));
        if (sessionCookie) {
          expect(sessionCookie).toContain('HttpOnly');
          expect(sessionCookie).toContain('SameSite=strict');
        }
      }
    });

    test('should handle concurrent sessions properly', async () => {
      // Login multiple times
      const loginPromises = [];
      for (let i = 0; i < 3; i++) {
        loginPromises.push(
          request(server)
            .post('/api/auth/login')
            .send({
              email: '<EMAIL>',
              password: 'TestPassword123!'
            })
        );
      }
      
      const responses = await Promise.all(loginPromises);
      const tokens = responses.map(res => res.body.accessToken);
      
      // All tokens should be valid
      for (const token of tokens) {
        const authResponse = await request(server)
          .get('/api/auth/me')
          .set('Authorization', `Bearer ${token}`);
        
        expect(authResponse.status).toBe(200);
      }
    });
  });

  describe('Error Handling', () => {
    test('should not leak sensitive information in errors', async () => {
      // Try to access non-existent user
      const response = await request(server)
        .get('/api/admin/users/507f1f77bcf86cd799439011')
        .set('Authorization', `Bearer ${adminToken}`);
      
      expect(response.status).toBe(404);
      expect(response.body.error).not.toContain('ObjectId');
      expect(response.body.error).not.toContain('mongoose');
      expect(response.body.stack).toBeUndefined();
    });

    test('should handle malformed requests gracefully', async () => {
      const response = await request(server)
        .post('/api/auth/login')
        .send('invalid-json')
        .set('Content-Type', 'application/json');
      
      expect(response.status).toBe(400);
      expect(response.body.error).toBeDefined();
    });
  });

  describe('Audit Logging', () => {
    test('should log security events', async () => {
      // Attempt failed login
      await request(server)
        .post('/api/auth/login')
        .send({
          email: '<EMAIL>',
          password: 'wrongpassword'
        });
      
      // Check if audit log was created
      const AuditLog = require('../models/AuditLog');
      const auditEntry = await AuditLog.findOne({
        eventType: 'login_failed',
        username: '<EMAIL>'
      });
      
      expect(auditEntry).toBeTruthy();
      expect(auditEntry.ipAddress).toBeDefined();
      expect(auditEntry.userAgent).toBeDefined();
    });

    test('should log administrative actions', async () => {
      // Create a user
      await request(server)
        .post('/api/admin/users')
        .send({
          username: 'audituser',
          email: '<EMAIL>',
          firstName: 'Audit',
          lastName: 'User',
          role: 'viewer'
        })
        .set('Authorization', `Bearer ${adminToken}`);
      
      // Check audit log
      const AuditLog = require('../models/AuditLog');
      const auditEntry = await AuditLog.findOne({
        eventType: 'user_created',
        targetName: 'audituser'
      });
      
      expect(auditEntry).toBeTruthy();
      expect(auditEntry.description).toContain('Created new user');
    });
  });
});
