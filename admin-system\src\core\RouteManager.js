const path = require('path');
const mongoose = require('mongoose');
const { logger } = require('../../utils/logger');

// Import route modules
const authRoutes = require('../../routes/auth');
const adminRoutes = require('../../routes/admin');
const contentRoutes = require('../../routes/content');
const publicRoutes = require('../../routes/public');
const datingProfileRoutes = require('../../routes/datingProfile');

/**
 * Route Manager
 * Handles all route setup and organization
 */
class RouteManager {
  constructor(app, middlewareManager) {
    this.app = app;
    this.middlewareManager = middlewareManager;
  }

  /**
   * Setup all application routes
   */
  async setup() {
    try {
      logger.info('Setting up routes...');

      // Health check and system routes
      this.setupSystemRoutes();
      
      // Public API routes
      this.setupPublicRoutes();
      
      // Authentication routes
      this.setupAuthRoutes();
      
      // Admin routes
      this.setupAdminRoutes();
      
      // Content management routes
      this.setupContentRoutes();
      
      // Dating profile routes
      this.setupDatingProfileRoutes();
      
      // SPA and fallback routes
      this.setupSpaRoutes();
      
      // Error handling routes
      this.setupErrorRoutes();

      logger.info('Routes setup completed successfully');
    } catch (error) {
      logger.error('Routes setup failed', { error: error.message });
      throw error;
    }
  }

  /**
   * Setup system routes (health check, etc.)
   */
  setupSystemRoutes() {
    // Health check endpoint (no auth required)
    this.app.get('/health', (req, res) => {
      const healthCheck = {
        status: 'OK',
        timestamp: new Date().toISOString(),
        uptime: process.uptime(),
        environment: process.env.NODE_ENV,
        version: process.env.APP_VERSION || '1.0.0',
        database: mongoose.connection.readyState === 1 ? 'connected' : 'disconnected',
        memory: process.memoryUsage(),
        pid: process.pid
      };

      res.status(200).json(healthCheck);
    });

    // CSRF token endpoint
    this.app.get('/api/csrf-token', (req, res) => {
      res.json({ csrfToken: req.csrfToken() });
    });

    // API info endpoint
    this.app.get('/api', (req, res) => {
      res.json({
        name: 'Blog Website API',
        version: process.env.APP_VERSION || '1.0.0',
        environment: process.env.NODE_ENV,
        timestamp: new Date().toISOString(),
        endpoints: {
          health: '/health',
          csrf: '/api/csrf-token',
          public: '/api/public/*',
          auth: '/api/auth/*',
          admin: '/api/admin/*',
          content: '/api/content/*',
          datingProfile: '/api/dating-profile/*'
        }
      });
    });
  }

  /**
   * Setup public API routes (read-only)
   */
  setupPublicRoutes() {
    this.app.use('/api/public', publicRoutes);
  }

  /**
   * Setup authentication routes
   */
  setupAuthRoutes() {
    this.app.use('/api/auth', authRoutes);
  }

  /**
   * Setup admin routes with proper middleware
   */
  setupAdminRoutes() {
    const adminMiddleware = this.middlewareManager ? 
      this.middlewareManager.getMiddleware('admin') : [];
    
    this.app.use('/api/admin', ...adminMiddleware, adminRoutes);
  }

  /**
   * Setup content management routes
   */
  setupContentRoutes() {
    const adminMiddleware = this.middlewareManager ? 
      this.middlewareManager.getMiddleware('admin') : [];
    
    this.app.use('/api/content', ...adminMiddleware, contentRoutes);
  }

  /**
   * Setup dating profile routes
   */
  setupDatingProfileRoutes() {
    this.app.use('/api/dating-profile', datingProfileRoutes);
  }

  /**
   * Setup SPA routes and redirects
   */
  setupSpaRoutes() {
    // Admin panel route (serve index.html for SPA)
    this.app.get('/admin/*', (req, res) => {
      res.sendFile(path.join(__dirname, '../../public/admin/index.html'));
    });

    // Dating profile route
    this.app.get('/profile.html', (req, res) => {
      res.sendFile(path.join(__dirname, '../../Dating-Resume-Profile/dating-resume-profile/src/index.html'));
    });

    // Root redirect to public website
    this.app.get('/', (req, res) => {
      res.sendFile(path.join(__dirname, '../../public/website/index.html'));
    });

    // Catch-all for SPA routing (should be last)
    this.app.get('*', (req, res) => {
      // Check if it's an API request that wasn't handled
      if (req.originalUrl.startsWith('/api/')) {
        return res.status(404).json({
          error: 'API endpoint not found',
          code: 'ENDPOINT_NOT_FOUND'
        });
      }

      // For non-API requests, serve the main website
      res.sendFile(path.join(__dirname, '../../public/website/index.html'));
    });
  }

  /**
   * Setup error handling routes
   */
  setupErrorRoutes() {
    // API 404 handler
    this.app.use('/api/*', (req, res) => {
      logger.warn('API endpoint not found', {
        method: req.method,
        url: req.originalUrl,
        ip: req.ip,
        userAgent: req.get('User-Agent')
      });

      res.status(404).json({
        error: 'API endpoint not found',
        code: 'ENDPOINT_NOT_FOUND',
        path: req.originalUrl,
        method: req.method
      });
    });
  }

  /**
   * Get route information for debugging
   */
  getRouteInfo() {
    const routes = [];
    
    this.app._router.stack.forEach((middleware) => {
      if (middleware.route) {
        // Direct route
        routes.push({
          path: middleware.route.path,
          methods: Object.keys(middleware.route.methods)
        });
      } else if (middleware.name === 'router') {
        // Router middleware
        middleware.handle.stack.forEach((handler) => {
          if (handler.route) {
            routes.push({
              path: handler.route.path,
              methods: Object.keys(handler.route.methods)
            });
          }
        });
      }
    });

    return routes;
  }

  /**
   * Register a new route dynamically
   */
  registerRoute(method, path, ...handlers) {
    try {
      this.app[method.toLowerCase()](path, ...handlers);
      logger.info('Route registered', { method, path });
    } catch (error) {
      logger.error('Failed to register route', { method, path, error: error.message });
      throw error;
    }
  }

  /**
   * Register multiple routes from configuration
   */
  registerRoutes(routeConfig) {
    routeConfig.forEach(({ method, path, handlers }) => {
      this.registerRoute(method, path, ...handlers);
    });
  }
}

module.exports = RouteManager;
