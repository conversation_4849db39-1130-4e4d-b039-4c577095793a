import { Chalk } from 'chalk';
import { SEVERITY } from '../../../snyk-test/common';
interface IacOutputColors {
    severities: SeverityColor;
    failure: Chalk;
    warning: Chalk;
    success: Chalk;
    info: Chalk;
    title: Chalk;
    suggestion: Chalk;
}
type SeverityColor = {
    [severity in SEVERITY]: Chalk;
};
export declare const colors: IacOutputColors;
export declare const contentPadding: string;
export declare const maxLineWidth: number;
export declare const countSuppressedIssues: (suppressedIssues: Record<string, string[]>) => number;
export {};
