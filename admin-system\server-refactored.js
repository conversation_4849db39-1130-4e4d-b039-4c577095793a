#!/usr/bin/env node

/**
 * Blog Website Server - Refactored
 * Enterprise-grade modular architecture
 */

require('dotenv').config();

const Application = require('./src/core/Application');
const { logger } = require('./utils/logger');

/**
 * Main server entry point
 */
async function startServer() {
  try {
    logger.info('Starting Blog Website Server...');
    
    // Create and start application
    const app = new Application();
    await app.start();
    
    logger.info('Server startup completed successfully');
    
  } catch (error) {
    logger.error('Server startup failed', { 
      error: error.message, 
      stack: error.stack 
    });
    process.exit(1);
  }
}

// Start the server if this file is run directly
if (require.main === module) {
  startServer();
}

module.exports = { startServer };
