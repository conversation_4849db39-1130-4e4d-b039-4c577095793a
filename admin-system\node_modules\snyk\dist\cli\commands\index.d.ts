export = commands;
declare const commands: {
    auth: (...args: any[]) => Promise<any>;
    config: (...args: any[]) => Promise<any>;
    'update-exclude-policy': (...args: any[]) => Promise<any>;
    describe: (...args: any[]) => Promise<any>;
    help: (...args: any[]) => Promise<any>;
    ignore: (...args: any[]) => Promise<any>;
    monitor: (...args: any[]) => Promise<any>;
    fix: (...args: any[]) => Promise<any>;
    policy: (...args: any[]) => Promise<any>;
    protect: (...args: any[]) => Promise<any>;
    test: (...args: any[]) => Promise<any>;
    version: (...args: any[]) => Promise<any>;
    about: (...args: any[]) => Promise<any>;
    wizard: (...args: any[]) => Promise<any>;
    woof: (...args: any[]) => Promise<any>;
    log4shell: (...args: any[]) => Promise<any>;
    apps: (...args: any[]) => Promise<any>;
};
