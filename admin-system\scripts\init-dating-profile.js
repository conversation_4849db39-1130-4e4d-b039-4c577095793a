#!/usr/bin/env node

/**
 * Dating Profile Initialization Script
 * Creates default dating profile data for the admin system
 */

const mongoose = require('mongoose');
const DatingProfile = require('../models/DatingProfile');
const User = require('../models/User');
require('dotenv').config();

const defaultProfileData = {
  profileId: 'main-profile',
  name: '<PERSON>',
  tagline: 'Adventure seeker with a passion for meaningful connections',
  age: 28,
  location: 'San Francisco, CA',
  profilePhoto: {
    url: 'https://randomuser.me/api/portraits/men/75.jpg',
    alt: '<PERSON> Profile Photo'
  },
  objective: 'I\'m looking for someone who shares my love for adventure and deep conversations. Whether it\'s hiking a new trail, trying a hole-in-the-wall restaurant, or discussing philosophy over coffee, I believe the best relationships are built on shared experiences and genuine connection.',
  personalSummary: 'I\'m a software engineer by day and an adventure enthusiast by weekend. I love exploring new places, whether that\'s a hidden hiking trail or a cozy bookstore in a different neighborhood. I value honesty, humor, and kindness above all else. My friends would describe me as reliable, curious, and someone who always brings good snacks to the party.',
  lookingFor: 'Someone who is genuine, has their own passions, and is excited about building something meaningful together. Bonus points if you love dogs, appreciate good coffee, and are up for spontaneous adventures.',
  relationshipQualifications: [
    {
      text: '5+ years of experience in meaningful relationships and personal growth',
      order: 1
    },
    {
      text: 'Excellent communication skills with proficiency in active listening',
      order: 2
    },
    {
      text: 'Strong emotional intelligence and conflict resolution abilities',
      order: 3
    },
    {
      text: 'Demonstrated commitment to personal development and self-awareness',
      order: 4
    },
    {
      text: 'Experience in building and maintaining trust in relationships',
      order: 5
    }
  ],
  skills: [
    {
      text: 'Active listening and empathetic communication',
      category: 'communication',
      order: 1
    },
    {
      text: 'Cooking delicious meals (specialty: Italian cuisine)',
      category: 'practical',
      order: 2
    },
    {
      text: 'Planning memorable dates and adventures',
      category: 'fun',
      order: 3
    },
    {
      text: 'Providing emotional support during tough times',
      category: 'emotional',
      order: 4
    },
    {
      text: 'Home improvement and DIY projects',
      category: 'practical',
      order: 5
    },
    {
      text: 'Making people laugh with terrible dad jokes',
      category: 'fun',
      order: 6
    }
  ],
  hobbies: [
    {
      text: 'Hiking and exploring national parks',
      category: 'outdoor',
      order: 1
    },
    {
      text: 'Reading science fiction and philosophy books',
      category: 'intellectual',
      order: 2
    },
    {
      text: 'Photography and capturing beautiful moments',
      category: 'creative',
      order: 3
    },
    {
      text: 'Rock climbing and bouldering',
      category: 'fitness',
      order: 4
    },
    {
      text: 'Cooking and experimenting with new recipes',
      category: 'indoor',
      order: 5
    },
    {
      text: 'Board game nights with friends',
      category: 'social',
      order: 6
    }
  ],
  personalGrowth: [
    {
      text: 'Completed a mindfulness meditation course to improve emotional awareness',
      type: 'learning',
      order: 1
    },
    {
      text: 'Read "The 7 Habits of Highly Effective People" and implemented daily planning',
      type: 'reading',
      order: 2
    },
    {
      text: 'Learned that vulnerability is a strength, not a weakness',
      type: 'lesson',
      order: 3
    },
    {
      text: 'Completed a marathon after 6 months of dedicated training',
      type: 'achievement',
      order: 4
    },
    {
      text: 'Working on becoming a better listener and asking more thoughtful questions',
      type: 'goal',
      order: 5
    }
  ],
  contactInfo: {
    email: '<EMAIL>',
    phone: '******-0123',
    socialMedia: {
      instagram: 'alexadventures',
      linkedin: 'alexjohnson-dev',
      twitter: 'alex_explores'
    },
    showContactAfterCaptcha: true
  },
  visibility: 'public',
  isActive: true,
  seo: {
    metaTitle: 'Alex Johnson - Dating Profile',
    metaDescription: 'Adventure seeker with a passion for meaningful connections. Software engineer who loves hiking, cooking, and deep conversations.',
    keywords: ['dating', 'adventure', 'hiking', 'software engineer', 'San Francisco', 'meaningful relationships']
  },
  theme: {
    primaryColor: '#0d6efd',
    backgroundColor: '#ffffff',
    fontFamily: 'Segoe UI'
  }
};

async function initializeDatingProfile() {
  try {
    console.log('🚀 Initializing dating profile...');

    // Connect to database
    await mongoose.connect(process.env.MONGODB_URI || 'mongodb://localhost:27017/fortune500_admin');
    console.log('✅ Connected to database');

    // Find or create admin user
    let adminUser = await User.findOne({ role: 'super_admin' });
    if (!adminUser) {
      adminUser = await User.findOne({ role: 'admin' });
    }
    
    if (!adminUser) {
      console.log('❌ No admin user found. Please create an admin user first.');
      process.exit(1);
    }

    console.log(`✅ Found admin user: ${adminUser.username}`);

    // Check if profile already exists
    const existingProfile = await DatingProfile.findOne({ profileId: 'main-profile' });
    
    if (existingProfile) {
      console.log('⚠️  Dating profile already exists. Updating...');
      
      // Update existing profile
      Object.assign(existingProfile, defaultProfileData);
      existingProfile.audit.updatedBy = adminUser._id;
      existingProfile.audit.updatedAt = new Date();
      
      await existingProfile.save();
      console.log('✅ Dating profile updated successfully');
    } else {
      console.log('📝 Creating new dating profile...');
      
      // Create new profile
      const profile = new DatingProfile({
        ...defaultProfileData,
        audit: {
          createdBy: adminUser._id,
          updatedBy: adminUser._id
        }
      });

      await profile.save();
      console.log('✅ Dating profile created successfully');
    }

    // Display profile information
    const profile = await DatingProfile.findOne({ profileId: 'main-profile' });
    console.log('\n📋 Profile Summary:');
    console.log(`   Name: ${profile.name}`);
    console.log(`   Tagline: ${profile.tagline}`);
    console.log(`   Location: ${profile.location}`);
    console.log(`   Visibility: ${profile.visibility}`);
    console.log(`   Active: ${profile.isActive ? 'Yes' : 'No'}`);
    console.log(`   Qualifications: ${profile.relationshipQualifications.length}`);
    console.log(`   Skills: ${profile.skills.length}`);
    console.log(`   Hobbies: ${profile.hobbies.length}`);
    console.log(`   Growth Items: ${profile.personalGrowth.length}`);
    console.log(`   Version: ${profile.audit.version}`);

    console.log('\n🎉 Dating profile initialization completed!');
    console.log('\n📍 Access URLs:');
    console.log(`   Public Profile: ${process.env.ADMIN_URL || 'http://localhost:3000'}/profile.html`);
    console.log(`   Admin Panel: ${process.env.ADMIN_URL || 'http://localhost:3000'}/admin`);

  } catch (error) {
    console.error('❌ Error initializing dating profile:', error);
    process.exit(1);
  } finally {
    await mongoose.connection.close();
    console.log('✅ Database connection closed');
  }
}

// Run the initialization
if (require.main === module) {
  initializeDatingProfile();
}

module.exports = { initializeDatingProfile, defaultProfileData };
