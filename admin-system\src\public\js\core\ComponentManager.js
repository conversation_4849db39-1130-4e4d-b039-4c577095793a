/**
 * Component Manager
 * Manages UI components with lifecycle and state management
 */
class ComponentManager {
  constructor() {
    this.components = new Map();
    this.componentInstances = new Map();
    this.globalState = new Map();
  }

  /**
   * Register a component class
   */
  register(name, componentClass) {
    if (typeof componentClass !== 'function') {
      throw new Error('Component must be a class or constructor function');
    }

    this.components.set(name, componentClass);
  }

  /**
   * Create component instance
   */
  create(name, element, options = {}) {
    const ComponentClass = this.components.get(name);
    if (!ComponentClass) {
      throw new Error(`Component '${name}' not found`);
    }

    const instanceId = this.generateInstanceId(name);
    const instance = new ComponentClass(element, options, instanceId);

    // Add lifecycle methods if not present
    this.addLifecycleMethods(instance);

    // Store instance
    this.componentInstances.set(instanceId, {
      name,
      instance,
      element,
      options,
      created: new Date()
    });

    // Initialize component
    if (typeof instance.init === 'function') {
      instance.init();
    }

    return instance;
  }

  /**
   * Get component instance by ID
   */
  getInstance(instanceId) {
    const componentData = this.componentInstances.get(instanceId);
    return componentData ? componentData.instance : null;
  }

  /**
   * Get all instances of a component type
   */
  getInstancesByType(name) {
    const instances = [];
    for (const [id, data] of this.componentInstances) {
      if (data.name === name) {
        instances.push(data.instance);
      }
    }
    return instances;
  }

  /**
   * Destroy component instance
   */
  destroy(instanceId) {
    const componentData = this.componentInstances.get(instanceId);
    if (!componentData) {
      return false;
    }

    const { instance } = componentData;

    // Call destroy lifecycle method
    if (typeof instance.destroy === 'function') {
      instance.destroy();
    }

    // Remove event listeners
    if (instance._eventListeners) {
      instance._eventListeners.forEach(({ element, event, handler }) => {
        element.removeEventListener(event, handler);
      });
    }

    // Remove from instances
    this.componentInstances.delete(instanceId);
    return true;
  }

  /**
   * Auto-initialize components from DOM
   */
  autoInit(container = document) {
    const elements = container.querySelectorAll('[data-component]');
    const instances = [];

    elements.forEach(element => {
      const componentName = element.dataset.component;
      const options = this.parseOptions(element.dataset.options);

      try {
        const instance = this.create(componentName, element, options);
        instances.push(instance);
      } catch (error) {
        console.error(`Failed to initialize component '${componentName}':`, error);
      }
    });

    return instances;
  }

  /**
   * Add lifecycle methods to component instance
   */
  addLifecycleMethods(instance) {
    // Event listener tracking
    instance._eventListeners = [];

    // Enhanced addEventListener
    const originalAddEventListener = instance.addEventListener || function() {};
    instance.addEventListener = (element, event, handler, options) => {
      element.addEventListener(event, handler, options);
      instance._eventListeners.push({ element, event, handler });
    };

    // State management
    instance._state = new Map();
    instance.setState = (key, value) => {
      const oldValue = instance._state.get(key);
      instance._state.set(key, value);
      
      // Emit state change event
      eventBus.emit('component:state-change', {
        instanceId: instance._instanceId,
        key,
        oldValue,
        newValue: value
      });

      // Call component's onStateChange if it exists
      if (typeof instance.onStateChange === 'function') {
        instance.onStateChange(key, value, oldValue);
      }
    };

    instance.getState = (key) => {
      return instance._state.get(key);
    };

    // Global state access
    instance.getGlobalState = (key) => {
      return this.globalState.get(key);
    };

    instance.setGlobalState = (key, value) => {
      this.setGlobalState(key, value);
    };
  }

  /**
   * Parse options from data attribute
   */
  parseOptions(optionsString) {
    if (!optionsString) {
      return {};
    }

    try {
      return JSON.parse(optionsString);
    } catch (error) {
      console.warn('Failed to parse component options:', optionsString);
      return {};
    }
  }

  /**
   * Generate unique instance ID
   */
  generateInstanceId(componentName) {
    const timestamp = Date.now();
    const random = Math.random().toString(36).substr(2, 5);
    return `${componentName}-${timestamp}-${random}`;
  }

  /**
   * Set global state
   */
  setGlobalState(key, value) {
    const oldValue = this.globalState.get(key);
    this.globalState.set(key, value);

    // Emit global state change event
    eventBus.emit('global:state-change', {
      key,
      oldValue,
      newValue: value
    });
  }

  /**
   * Get global state
   */
  getGlobalState(key) {
    return this.globalState.get(key);
  }

  /**
   * Get component statistics
   */
  getStats() {
    const stats = {
      registeredComponents: this.components.size,
      activeInstances: this.componentInstances.size,
      instancesByType: {}
    };

    for (const [id, data] of this.componentInstances) {
      if (!stats.instancesByType[data.name]) {
        stats.instancesByType[data.name] = 0;
      }
      stats.instancesByType[data.name]++;
    }

    return stats;
  }

  /**
   * Cleanup all components
   */
  cleanup() {
    const instanceIds = Array.from(this.componentInstances.keys());
    instanceIds.forEach(id => this.destroy(id));
    this.globalState.clear();
  }

  /**
   * Debug information
   */
  debug() {
    console.group('ComponentManager Debug Info');
    console.log('Registered Components:', Array.from(this.components.keys()));
    console.log('Active Instances:', this.componentInstances.size);
    console.log('Global State:', Object.fromEntries(this.globalState));
    console.log('Stats:', this.getStats());
    console.groupEnd();
  }
}

/**
 * Base Component Class
 */
class BaseComponent {
  constructor(element, options = {}, instanceId = null) {
    this.element = element;
    this.options = { ...this.getDefaultOptions(), ...options };
    this._instanceId = instanceId;
    this._initialized = false;
  }

  /**
   * Get default options (override in subclasses)
   */
  getDefaultOptions() {
    return {};
  }

  /**
   * Initialize component (override in subclasses)
   */
  init() {
    this._initialized = true;
  }

  /**
   * Destroy component (override in subclasses)
   */
  destroy() {
    this._initialized = false;
  }

  /**
   * Check if component is initialized
   */
  isInitialized() {
    return this._initialized;
  }

  /**
   * Find element within component
   */
  find(selector) {
    return this.element.querySelector(selector);
  }

  /**
   * Find all elements within component
   */
  findAll(selector) {
    return this.element.querySelectorAll(selector);
  }

  /**
   * Add CSS class to component element
   */
  addClass(className) {
    this.element.classList.add(className);
  }

  /**
   * Remove CSS class from component element
   */
  removeClass(className) {
    this.element.classList.remove(className);
  }

  /**
   * Toggle CSS class on component element
   */
  toggleClass(className) {
    this.element.classList.toggle(className);
  }

  /**
   * Check if component element has CSS class
   */
  hasClass(className) {
    return this.element.classList.contains(className);
  }
}

// Create singleton instance
const componentManager = new ComponentManager();

// Export for use in other modules
window.ComponentManager = ComponentManager;
window.BaseComponent = BaseComponent;
window.componentManager = componentManager;
