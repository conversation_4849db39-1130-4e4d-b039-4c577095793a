const express = require('express');
const { body, query, validationResult } = require('express-validator');
const Content = require('../models/Content');
const AuditLog = require('../models/AuditLog');
const { authenticateJWT, requirePermission, auditLog } = require('../middleware/auth');
const { apiRateLimit } = require('../middleware/security');
const { logger } = require('../utils/logger');

const router = express.Router();

/**
 * Content Management API Routes
 * Secure endpoints for content CRUD operations with approval workflow
 */

// Apply authentication and rate limiting
router.use(authenticateJWT);
router.use(apiRateLimit);

// Validation middleware
const validatePagination = [
  query('page').optional().isInt({ min: 1 }).toInt(),
  query('limit').optional().isInt({ min: 1, max: 100 }).toInt(),
  query('sort').optional().isString().trim(),
  query('order').optional().isIn(['asc', 'desc'])
];

const validateContentCreation = [
  body('title').isLength({ min: 1, max: 200 }).trim(),
  body('slug').isLength({ min: 1, max: 200 }).matches(/^[a-z0-9-]+$/),
  body('content').isLength({ min: 1 }),
  body('type').isIn(['post', 'page', 'profile_section']),
  body('status').optional().isIn(['draft', 'pending_review']),
  body('excerpt').optional().isLength({ max: 500 }).trim(),
  body('category').optional().isString().trim(),
  body('tags').optional().isArray(),
  body('tags.*').optional().isString().trim()
];

const validateContentUpdate = [
  body('title').optional().isLength({ min: 1, max: 200 }).trim(),
  body('content').optional().isLength({ min: 1 }),
  body('excerpt').optional().isLength({ max: 500 }).trim(),
  body('category').optional().isString().trim(),
  body('tags').optional().isArray(),
  body('tags.*').optional().isString().trim(),
  body('status').optional().isIn(['draft', 'pending_review', 'approved', 'published', 'archived'])
];

// Get all content with filtering and pagination
router.get('/',
  requirePermission('content', 'read'),
  validatePagination,
  async (req, res) => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({ error: 'Validation failed', details: errors.array() });
      }

      const page = req.query.page || 1;
      const limit = req.query.limit || 20;
      const skip = (page - 1) * limit;
      const search = req.query.search;
      const type = req.query.type;
      const status = req.query.status;
      const author = req.query.author;
      const category = req.query.category;

      // Build query
      const query = {};
      if (search) {
        query.$text = { $search: search };
      }
      if (type) query.type = type;
      if (status) query.status = status;
      if (author) query.author = author;
      if (category) query.category = category;

      // User can only see their own content unless they have manage permission
      if (!req.user.hasPermission('content', 'manage') && req.user.role !== 'admin' && req.user.role !== 'super_admin') {
        query.$or = [
          { author: req.user._id },
          { 'contributors.user': req.user._id },
          { status: 'published' }
        ];
      }

      const sortField = req.query.sort || 'createdAt';
      const sortOrder = req.query.order === 'asc' ? 1 : -1;
      const sort = { [sortField]: sortOrder };

      const [content, total] = await Promise.all([
        Content.find(query)
          .populate('author', 'username firstName lastName')
          .populate('contributors.user', 'username firstName lastName')
          .populate('workflow.assignedTo', 'username firstName lastName')
          .sort(sort)
          .skip(skip)
          .limit(limit)
          .lean(),
        Content.countDocuments(query)
      ]);

      res.json({
        content,
        pagination: {
          page,
          limit,
          total,
          pages: Math.ceil(total / limit)
        }
      });
    } catch (error) {
      logger.error('Error fetching content', { error: error.message, userId: req.user._id });
      res.status(500).json({ error: 'Failed to fetch content' });
    }
  }
);

// Get single content item
router.get('/:id',
  requirePermission('content', 'read'),
  async (req, res) => {
    try {
      const content = await Content.findById(req.params.id)
        .populate('author', 'username firstName lastName email')
        .populate('contributors.user', 'username firstName lastName')
        .populate('workflow.assignedTo', 'username firstName lastName')
        .populate('comments.author', 'username firstName lastName');

      if (!content) {
        return res.status(404).json({ error: 'Content not found' });
      }

      // Check if user can view this content
      if (!content.canEdit(req.user) && content.status !== 'published' && 
          !req.user.hasPermission('content', 'manage')) {
        return res.status(403).json({ error: 'Access denied' });
      }

      res.json({ content });
    } catch (error) {
      logger.error('Error fetching content', { error: error.message, userId: req.user._id });
      res.status(500).json({ error: 'Failed to fetch content' });
    }
  }
);

// Create new content
router.post('/',
  requirePermission('content', 'create'),
  validateContentCreation,
  auditLog('content_create', 'content'),
  async (req, res) => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({ error: 'Validation failed', details: errors.array() });
      }

      const { title, slug, content, type, excerpt, category, tags, status } = req.body;

      // Check if slug already exists
      const existingContent = await Content.findOne({ slug });
      if (existingContent) {
        return res.status(409).json({ error: 'Slug already exists' });
      }

      const newContent = new Content({
        title,
        slug,
        content,
        type,
        excerpt,
        category,
        tags: tags || [],
        status: status || 'draft',
        author: req.user._id,
        audit: {
          createdBy: req.user._id
        }
      });

      await newContent.save();

      // Log content creation
      await AuditLog.logEvent({
        eventType: 'content_created',
        userId: req.user._id,
        username: req.user.username,
        userRole: req.user.role,
        targetType: 'content',
        targetId: newContent._id.toString(),
        targetName: newContent.title,
        action: 'create',
        resource: 'content',
        description: `Created new ${type}: ${title}`,
        ipAddress: req.ip,
        userAgent: req.get('User-Agent'),
        method: req.method,
        endpoint: req.originalUrl,
        severity: 'low'
      });

      res.status(201).json({
        message: 'Content created successfully',
        content: {
          id: newContent._id,
          title: newContent.title,
          slug: newContent.slug,
          type: newContent.type,
          status: newContent.status
        }
      });
    } catch (error) {
      logger.error('Error creating content', { error: error.message, userId: req.user._id });
      res.status(500).json({ error: 'Failed to create content' });
    }
  }
);

// Update content
router.put('/:id',
  validateContentUpdate,
  auditLog('content_update', 'content'),
  async (req, res) => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({ error: 'Validation failed', details: errors.array() });
      }

      const content = await Content.findById(req.params.id);
      if (!content) {
        return res.status(404).json({ error: 'Content not found' });
      }

      // Check permissions
      if (!content.canEdit(req.user)) {
        return res.status(403).json({ error: 'Permission denied' });
      }

      // Store original values for audit
      const originalValues = {
        title: content.title,
        status: content.status,
        category: content.category
      };

      // Update content
      const updates = req.body;
      Object.assign(content, updates);
      content.audit.updatedBy = req.user._id;
      content.audit.updatedAt = new Date();

      await content.save();

      // Log content update
      await AuditLog.logEvent({
        eventType: 'content_updated',
        userId: req.user._id,
        username: req.user.username,
        userRole: req.user.role,
        targetType: 'content',
        targetId: content._id.toString(),
        targetName: content.title,
        action: 'update',
        resource: 'content',
        description: `Updated ${content.type}: ${content.title}`,
        changes: { before: originalValues, after: updates },
        ipAddress: req.ip,
        userAgent: req.get('User-Agent'),
        method: req.method,
        endpoint: req.originalUrl,
        severity: 'low'
      });

      res.json({
        message: 'Content updated successfully',
        content: {
          id: content._id,
          title: content.title,
          status: content.status,
          version: content.version
        }
      });
    } catch (error) {
      logger.error('Error updating content', { error: error.message, userId: req.user._id });
      res.status(500).json({ error: 'Failed to update content' });
    }
  }
);

// Publish content
router.post('/:id/publish',
  auditLog('content_publish', 'content'),
  async (req, res) => {
    try {
      const content = await Content.findById(req.params.id);
      if (!content) {
        return res.status(404).json({ error: 'Content not found' });
      }

      // Check permissions
      if (!content.canPublish(req.user)) {
        return res.status(403).json({ error: 'Permission denied' });
      }

      await content.publish(req.user._id);

      // Log content publication
      await AuditLog.logEvent({
        eventType: 'content_published',
        userId: req.user._id,
        username: req.user.username,
        userRole: req.user.role,
        targetType: 'content',
        targetId: content._id.toString(),
        targetName: content.title,
        action: 'publish',
        resource: 'content',
        description: `Published ${content.type}: ${content.title}`,
        ipAddress: req.ip,
        userAgent: req.get('User-Agent'),
        method: req.method,
        endpoint: req.originalUrl,
        severity: 'medium'
      });

      res.json({
        message: 'Content published successfully',
        content: {
          id: content._id,
          title: content.title,
          status: content.status,
          publishedAt: content.publishedAt
        }
      });
    } catch (error) {
      logger.error('Error publishing content', { error: error.message, userId: req.user._id });
      res.status(500).json({ error: 'Failed to publish content' });
    }
  }
);

// Archive content
router.post('/:id/archive',
  requirePermission('content', 'delete'),
  auditLog('content_archive', 'content'),
  async (req, res) => {
    try {
      const content = await Content.findById(req.params.id);
      if (!content) {
        return res.status(404).json({ error: 'Content not found' });
      }

      await content.archive(req.user._id);

      // Log content archival
      await AuditLog.logEvent({
        eventType: 'content_archived',
        userId: req.user._id,
        username: req.user.username,
        userRole: req.user.role,
        targetType: 'content',
        targetId: content._id.toString(),
        targetName: content.title,
        action: 'archive',
        resource: 'content',
        description: `Archived ${content.type}: ${content.title}`,
        ipAddress: req.ip,
        userAgent: req.get('User-Agent'),
        method: req.method,
        endpoint: req.originalUrl,
        severity: 'medium'
      });

      res.json({
        message: 'Content archived successfully',
        content: {
          id: content._id,
          title: content.title,
          status: content.status
        }
      });
    } catch (error) {
      logger.error('Error archiving content', { error: error.message, userId: req.user._id });
      res.status(500).json({ error: 'Failed to archive content' });
    }
  }
);

// Add comment to content
router.post('/:id/comments',
  requirePermission('content', 'read'),
  [body('content').isLength({ min: 1, max: 1000 }).trim()],
  async (req, res) => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({ error: 'Validation failed', details: errors.array() });
      }

      const content = await Content.findById(req.params.id);
      if (!content) {
        return res.status(404).json({ error: 'Content not found' });
      }

      await content.addComment(req.user._id, req.body.content, 'comment', true);

      res.json({ message: 'Comment added successfully' });
    } catch (error) {
      logger.error('Error adding comment', { error: error.message, userId: req.user._id });
      res.status(500).json({ error: 'Failed to add comment' });
    }
  }
);

module.exports = router;
