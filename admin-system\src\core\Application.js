const express = require('express');
const { logger } = require('../../utils/logger');
const DatabaseManager = require('./DatabaseManager');
const MiddlewareManager = require('./MiddlewareManager');
const RouteManager = require('./RouteManager');
const ErrorManager = require('./ErrorManager');
const ConfigManager = require('./ConfigManager');

/**
 * Main Application Class
 * Orchestrates the entire application lifecycle
 */
class Application {
  constructor() {
    this.app = express();
    this.server = null;
    this.port = process.env.PORT || 3000;
    
    // Initialize managers
    this.config = new ConfigManager();
    this.database = new DatabaseManager();
    this.middleware = new MiddlewareManager(this.app);
    this.routes = new RouteManager(this.app);
    this.errors = new ErrorManager(this.app);
  }

  /**
   * Initialize the application
   */
  async initialize() {
    try {
      logger.info('Starting application initialization...');
      
      // Validate configuration
      await this.config.validate();
      
      // Connect to database
      await this.database.connect();
      
      // Setup middleware
      await this.middleware.setup();
      
      // Setup routes
      await this.routes.setup();
      
      // Setup error handling
      await this.errors.setup();
      
      logger.info('Application initialization completed successfully');
      
    } catch (error) {
      logger.error('Application initialization failed', { error: error.message });
      throw error;
    }
  }

  /**
   * Start the server
   */
  async start() {
    try {
      await this.initialize();
      
      this.server = this.app.listen(this.port, () => {
        logger.info('Server started successfully', {
          port: this.port,
          environment: process.env.NODE_ENV,
          pid: process.pid,
          nodeVersion: process.version,
          platform: process.platform
        });

        // Log configuration status
        this.logConfigurationStatus();
      });

      // Setup server error handling
      this.setupServerErrorHandling();
      
      // Setup graceful shutdown
      this.setupGracefulShutdown();
      
    } catch (error) {
      logger.error('Server startup failed', { error: error.message });
      process.exit(1);
    }
  }

  /**
   * Setup server error handling
   */
  setupServerErrorHandling() {
    this.server.on('error', (error) => {
      if (error.code === 'EADDRINUSE') {
        logger.error(`Port ${this.port} is already in use`);
      } else {
        logger.error('Server error', { error: error.message });
      }
      process.exit(1);
    });

    // Handle uncaught exceptions
    process.on('uncaughtException', (error) => {
      logger.error('Uncaught exception', { error: error.message, stack: error.stack });
      this.gracefulShutdown('SIGTERM');
    });

    // Handle unhandled promise rejections
    process.on('unhandledRejection', (reason, promise) => {
      logger.error('Unhandled promise rejection', { reason, promise });
      this.gracefulShutdown('SIGTERM');
    });
  }

  /**
   * Setup graceful shutdown handlers
   */
  setupGracefulShutdown() {
    const signals = ['SIGTERM', 'SIGINT', 'SIGUSR2'];
    
    signals.forEach(signal => {
      process.on(signal, () => {
        this.gracefulShutdown(signal);
      });
    });
  }

  /**
   * Perform graceful shutdown
   */
  async gracefulShutdown(signal) {
    logger.info(`Received ${signal}, starting graceful shutdown`);

    // Stop accepting new connections
    if (this.server) {
      this.server.close(async () => {
        logger.info('HTTP server closed');

        try {
          // Close database connection
          await this.database.disconnect();
          logger.info('Database connection closed');

          // Cleanup other resources
          await this.cleanup();

          logger.info('Graceful shutdown completed');
          process.exit(0);
        } catch (error) {
          logger.error('Error during graceful shutdown', { error: error.message });
          process.exit(1);
        }
      });
    }

    // Force shutdown after timeout
    setTimeout(() => {
      logger.error('Forced shutdown after timeout');
      process.exit(1);
    }, 30000);
  }

  /**
   * Cleanup resources
   */
  async cleanup() {
    // Add any additional cleanup logic here
    logger.info('Cleanup completed');
  }

  /**
   * Log configuration status
   */
  logConfigurationStatus() {
    logger.info('Security configuration loaded', {
      jwtEnabled: !!process.env.JWT_SECRET,
      sessionEnabled: !!process.env.SESSION_SECRET,
      encryptionEnabled: !!process.env.ENCRYPTION_KEY,
      corsEnabled: true,
      rateLimitEnabled: true,
      csrfEnabled: true
    });
  }

  /**
   * Get Express app instance
   */
  getApp() {
    return this.app;
  }

  /**
   * Get server instance
   */
  getServer() {
    return this.server;
  }
}

module.exports = Application;
