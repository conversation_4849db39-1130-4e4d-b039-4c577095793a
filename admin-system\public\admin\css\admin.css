/**
 * Fortune 500 Admin Panel Styles
 * Enterprise-grade design with security-focused UI/UX
 */

/* CSS Variables for Theming */
:root {
  --primary-color: #0d6efd;
  --secondary-color: #6c757d;
  --success-color: #198754;
  --danger-color: #dc3545;
  --warning-color: #ffc107;
  --info-color: #0dcaf0;
  --dark-color: #212529;
  --light-color: #f8f9fa;
  
  --sidebar-width: 250px;
  --navbar-height: 60px;
  
  --border-radius: 8px;
  --box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  --transition: all 0.3s ease;
}

/* Global Styles */
* {
  box-sizing: border-box;
}

body.admin-body {
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  margin: 0;
  padding: 0;
  min-height: 100vh;
  overflow-x: hidden;
}

/* Loading Overlay */
.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(255, 255, 255, 0.95);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
  transition: opacity 0.3s ease;
}

.loading-spinner {
  text-align: center;
}

.loading-text {
  margin-top: 1rem;
  color: var(--secondary-color);
  font-weight: 500;
}

/* Login Screen */
.login-screen {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 2rem;
  position: relative;
}

.login-container {
  background: white;
  border-radius: var(--border-radius);
  box-shadow: var(--box-shadow);
  padding: 3rem;
  width: 100%;
  max-width: 400px;
  position: relative;
  z-index: 2;
}

.login-header {
  text-align: center;
  margin-bottom: 2rem;
}

.login-header h1 {
  color: var(--dark-color);
  font-weight: 700;
  margin-bottom: 0.5rem;
}

.login-header h1 i {
  color: var(--primary-color);
  margin-right: 0.5rem;
}

.login-form .form-control {
  border-radius: var(--border-radius);
  border: 2px solid #e9ecef;
  padding: 0.75rem;
  transition: var(--transition);
}

.login-form .form-control:focus {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
}

.login-form .input-group-text {
  background: var(--light-color);
  border: 2px solid #e9ecef;
  border-right: none;
}

.login-form .btn-primary {
  background: var(--primary-color);
  border: none;
  border-radius: var(--border-radius);
  padding: 0.75rem;
  font-weight: 600;
  transition: var(--transition);
}

.login-form .btn-primary:hover {
  background: #0b5ed7;
  transform: translateY(-1px);
}

.login-footer {
  text-align: center;
  margin-top: 1.5rem;
}

.login-footer a {
  color: var(--primary-color);
  text-decoration: none;
  font-size: 0.9rem;
}

.login-footer a:hover {
  text-decoration: underline;
}

.security-notice {
  position: absolute;
  bottom: 2rem;
  left: 0;
  right: 0;
  z-index: 1;
}

/* Admin Interface */
.admin-interface {
  min-height: 100vh;
  background: var(--light-color);
}

.admin-navbar {
  height: var(--navbar-height);
  box-shadow: var(--box-shadow);
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
}

.admin-navbar .navbar-brand {
  font-weight: 700;
  font-size: 1.25rem;
}

.user-avatar {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  object-fit: cover;
}

/* Main Layout */
.admin-main {
  display: flex;
  margin-top: var(--navbar-height);
  min-height: calc(100vh - var(--navbar-height));
}

.admin-sidebar {
  width: var(--sidebar-width);
  background: white;
  box-shadow: var(--box-shadow);
  position: fixed;
  top: var(--navbar-height);
  left: 0;
  height: calc(100vh - var(--navbar-height));
  overflow-y: auto;
  z-index: 999;
}

.sidebar-content {
  padding: 1.5rem 1rem;
}

.admin-sidebar .nav-link {
  color: var(--dark-color);
  border-radius: var(--border-radius);
  margin-bottom: 0.5rem;
  padding: 0.75rem 1rem;
  transition: var(--transition);
  display: flex;
  align-items: center;
}

.admin-sidebar .nav-link i {
  margin-right: 0.75rem;
  width: 20px;
  text-align: center;
}

.admin-sidebar .nav-link:hover {
  background: var(--light-color);
  color: var(--primary-color);
}

.admin-sidebar .nav-link.active {
  background: var(--primary-color);
  color: white;
}

.admin-content {
  flex: 1;
  margin-left: var(--sidebar-width);
  padding: 2rem;
  background: var(--light-color);
}

/* Content Sections */
.content-section {
  display: none;
}

.content-section.active {
  display: block;
}

.section-header {
  display: flex;
  justify-content: between;
  align-items: center;
  margin-bottom: 2rem;
  padding-bottom: 1rem;
  border-bottom: 2px solid #e9ecef;
}

.section-header h2 {
  margin: 0;
  color: var(--dark-color);
  font-weight: 600;
}

.section-header h2 i {
  color: var(--primary-color);
  margin-right: 0.5rem;
}

.section-actions {
  display: flex;
  gap: 0.5rem;
}

/* Dashboard Stats */
.stat-card {
  background: white;
  border-radius: var(--border-radius);
  padding: 1.5rem;
  box-shadow: var(--box-shadow);
  display: flex;
  align-items: center;
  transition: var(--transition);
  height: 100%;
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
}

.stat-icon {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 1rem;
  font-size: 1.5rem;
  color: white;
}

.stat-content h3 {
  margin: 0;
  font-size: 2rem;
  font-weight: 700;
  color: var(--dark-color);
}

.stat-content p {
  margin: 0;
  color: var(--secondary-color);
  font-size: 0.9rem;
}

/* Cards */
.card {
  border: none;
  border-radius: var(--border-radius);
  box-shadow: var(--box-shadow);
  margin-bottom: 1.5rem;
}

.card-header {
  background: white;
  border-bottom: 2px solid var(--light-color);
  padding: 1rem 1.5rem;
}

.card-header h5 {
  margin: 0;
  font-weight: 600;
  color: var(--dark-color);
}

.card-header h5 i {
  color: var(--primary-color);
  margin-right: 0.5rem;
}

.card-body {
  padding: 1.5rem;
}

/* Activity List */
.activity-list {
  max-height: 400px;
  overflow-y: auto;
}

.activity-item {
  display: flex;
  align-items: center;
  padding: 0.75rem 0;
  border-bottom: 1px solid var(--light-color);
}

.activity-item:last-child {
  border-bottom: none;
}

.activity-icon {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 1rem;
  font-size: 1rem;
  color: white;
}

.activity-content {
  flex: 1;
}

.activity-title {
  font-weight: 600;
  color: var(--dark-color);
  margin-bottom: 0.25rem;
}

.activity-description {
  color: var(--secondary-color);
  font-size: 0.9rem;
  margin: 0;
}

.activity-time {
  color: var(--secondary-color);
  font-size: 0.8rem;
  white-space: nowrap;
}

/* Security Status */
.security-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.5rem 0;
  border-bottom: 1px solid var(--light-color);
}

.security-item:last-child {
  border-bottom: none;
}

.security-label {
  font-weight: 500;
  color: var(--dark-color);
}

/* Loading Placeholder */
.loading-placeholder {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 300px;
  color: var(--secondary-color);
}

/* Responsive Design */
@media (max-width: 768px) {
  .admin-sidebar {
    transform: translateX(-100%);
    transition: transform 0.3s ease;
  }
  
  .admin-sidebar.show {
    transform: translateX(0);
  }
  
  .admin-content {
    margin-left: 0;
    padding: 1rem;
  }
  
  .login-container {
    padding: 2rem;
    margin: 1rem;
  }
  
  .section-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 1rem;
  }
  
  .stat-card {
    margin-bottom: 1rem;
  }
}

/* Accessibility */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* Focus Styles */
.btn:focus,
.form-control:focus,
.nav-link:focus {
  outline: 2px solid var(--primary-color);
  outline-offset: 2px;
}

/* High Contrast Mode */
@media (prefers-contrast: high) {
  .card,
  .stat-card,
  .login-container {
    border: 2px solid var(--dark-color);
  }
  
  .nav-link:hover,
  .nav-link.active {
    border: 2px solid var(--primary-color);
  }
}

/* Print Styles */
@media print {
  .admin-navbar,
  .admin-sidebar,
  .section-actions {
    display: none !important;
  }
  
  .admin-content {
    margin-left: 0;
    padding: 0;
  }
  
  .card {
    box-shadow: none;
    border: 1px solid #ccc;
  }
}
