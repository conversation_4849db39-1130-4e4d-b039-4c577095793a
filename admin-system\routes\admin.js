const express = require('express');
const { body, query, validationResult } = require('express-validator');
const User = require('../models/User');
const Content = require('../models/Content');
const AuditLog = require('../models/AuditLog');
const { authenticateJWT, authorize, requirePermission, auditLog } = require('../middleware/auth');
const { apiRateLimit } = require('../middleware/security');
const { logger, securityLogger } = require('../utils/logger');

const router = express.Router();

/**
 * Admin API Routes
 * Secure endpoints for admin panel functionality
 */

// Apply authentication and rate limiting to all admin routes
router.use(authenticateJWT);
router.use(apiRateLimit);

// Input validation middleware
const validatePagination = [
  query('page').optional().isInt({ min: 1 }).toInt(),
  query('limit').optional().isInt({ min: 1, max: 100 }).toInt(),
  query('sort').optional().isString().trim(),
  query('order').optional().isIn(['asc', 'desc'])
];

const validateUserCreation = [
  body('username').isLength({ min: 3, max: 50 }).matches(/^[a-zA-Z0-9_-]+$/),
  body('email').isEmail().normalizeEmail(),
  body('firstName').isLength({ min: 1, max: 50 }).trim(),
  body('lastName').isLength({ min: 1, max: 50 }).trim(),
  body('role').isIn(['admin', 'editor', 'viewer']),
  body('department').optional().isLength({ max: 100 }).trim()
];

// Dashboard Statistics
router.get('/stats/users', 
  requirePermission('users', 'read'),
  async (req, res) => {
    try {
      const [total, active, locked, newThisMonth] = await Promise.all([
        User.countDocuments(),
        User.countDocuments({ status: 'active' }),
        User.countDocuments({ 'security.lockUntil': { $gt: new Date() } }),
        User.countDocuments({
          'audit.createdAt': { 
            $gte: new Date(new Date().getFullYear(), new Date().getMonth(), 1) 
          }
        })
      ]);

      res.json({
        total,
        active,
        locked,
        newThisMonth,
        inactive: total - active
      });
    } catch (error) {
      logger.error('Error fetching user stats', { error: error.message, userId: req.user._id });
      res.status(500).json({ error: 'Failed to fetch user statistics' });
    }
  }
);

router.get('/stats/content',
  requirePermission('content', 'read'),
  async (req, res) => {
    try {
      const [total, published, draft, pendingReview] = await Promise.all([
        Content.countDocuments(),
        Content.countDocuments({ status: 'published' }),
        Content.countDocuments({ status: 'draft' }),
        Content.countDocuments({ status: 'pending_review' })
      ]);

      res.json({
        total,
        published,
        draft,
        pendingReview,
        archived: total - published - draft - pendingReview
      });
    } catch (error) {
      logger.error('Error fetching content stats', { error: error.message, userId: req.user._id });
      res.status(500).json({ error: 'Failed to fetch content statistics' });
    }
  }
);

router.get('/stats/security',
  requirePermission('security', 'read'),
  async (req, res) => {
    try {
      const last24Hours = new Date(Date.now() - 24 * 60 * 60 * 1000);
      
      const [failedLogins, securityAlerts, activeSessions, suspiciousActivity] = await Promise.all([
        AuditLog.countDocuments({
          eventType: 'login_failed',
          timestamp: { $gte: last24Hours }
        }),
        AuditLog.countDocuments({
          severity: { $in: ['high', 'critical'] },
          timestamp: { $gte: last24Hours }
        }),
        User.aggregate([
          { $unwind: '$security.activeSessions' },
          { $count: 'total' }
        ]),
        AuditLog.countDocuments({
          eventType: 'suspicious_activity',
          timestamp: { $gte: last24Hours }
        })
      ]);

      res.json({
        failedLogins,
        alerts: securityAlerts,
        activeSessions: activeSessions[0]?.total || 0,
        threats: suspiciousActivity
      });
    } catch (error) {
      logger.error('Error fetching security stats', { error: error.message, userId: req.user._id });
      res.status(500).json({ error: 'Failed to fetch security statistics' });
    }
  }
);

// Recent Activity
router.get('/activity/recent',
  requirePermission('audit', 'read'),
  validatePagination,
  async (req, res) => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({ error: 'Validation failed', details: errors.array() });
      }

      const page = req.query.page || 1;
      const limit = req.query.limit || 20;
      const skip = (page - 1) * limit;

      const activities = await AuditLog.find()
        .sort({ timestamp: -1 })
        .skip(skip)
        .limit(limit)
        .populate('userId', 'username email role')
        .lean();

      res.json({
        activities,
        pagination: {
          page,
          limit,
          total: await AuditLog.countDocuments()
        }
      });
    } catch (error) {
      logger.error('Error fetching recent activity', { error: error.message, userId: req.user._id });
      res.status(500).json({ error: 'Failed to fetch recent activity' });
    }
  }
);

// User Management
router.get('/users',
  requirePermission('users', 'read'),
  validatePagination,
  async (req, res) => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({ error: 'Validation failed', details: errors.array() });
      }

      const page = req.query.page || 1;
      const limit = req.query.limit || 20;
      const skip = (page - 1) * limit;
      const search = req.query.search;
      const role = req.query.role;
      const status = req.query.status;

      // Build query
      const query = {};
      if (search) {
        query.$or = [
          { username: { $regex: search, $options: 'i' } },
          { email: { $regex: search, $options: 'i' } },
          { firstName: { $regex: search, $options: 'i' } },
          { lastName: { $regex: search, $options: 'i' } }
        ];
      }
      if (role) query.role = role;
      if (status) query.status = status;

      const [users, total] = await Promise.all([
        User.find(query)
          .select('-password -mfa.secret -security.passwordHistory -security.refreshTokens')
          .sort({ 'audit.createdAt': -1 })
          .skip(skip)
          .limit(limit)
          .lean(),
        User.countDocuments(query)
      ]);

      res.json({
        users,
        pagination: {
          page,
          limit,
          total,
          pages: Math.ceil(total / limit)
        }
      });
    } catch (error) {
      logger.error('Error fetching users', { error: error.message, userId: req.user._id });
      res.status(500).json({ error: 'Failed to fetch users' });
    }
  }
);

router.post('/users',
  authorize('super_admin', 'admin'),
  validateUserCreation,
  auditLog('user_create', 'users'),
  async (req, res) => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({ error: 'Validation failed', details: errors.array() });
      }

      const { username, email, firstName, lastName, role, department } = req.body;

      // Check if user already exists
      const existingUser = await User.findOne({
        $or: [{ email }, { username }]
      });

      if (existingUser) {
        return res.status(409).json({
          error: 'User already exists',
          field: existingUser.email === email ? 'email' : 'username'
        });
      }

      // Generate temporary password
      const tempPassword = require('crypto').randomBytes(12).toString('base64');

      const user = new User({
        username,
        email,
        firstName,
        lastName,
        role,
        department,
        password: tempPassword,
        status: 'pending_verification',
        audit: {
          createdBy: req.user._id
        }
      });

      await user.save();

      // Log user creation
      await AuditLog.logEvent({
        eventType: 'user_created',
        userId: req.user._id,
        username: req.user.username,
        userRole: req.user.role,
        targetType: 'user',
        targetId: user._id.toString(),
        targetName: user.username,
        action: 'create',
        resource: 'users',
        description: `Created new user: ${user.username} (${user.email})`,
        ipAddress: req.ip,
        userAgent: req.get('User-Agent'),
        method: req.method,
        endpoint: req.originalUrl,
        severity: 'medium'
      });

      // TODO: Send welcome email with temporary password
      
      res.status(201).json({
        message: 'User created successfully',
        user: {
          id: user._id,
          username: user.username,
          email: user.email,
          role: user.role,
          status: user.status
        },
        tempPassword // In production, this should be sent via email only
      });

    } catch (error) {
      logger.error('Error creating user', { error: error.message, userId: req.user._id });
      res.status(500).json({ error: 'Failed to create user' });
    }
  }
);

router.put('/users/:id',
  authorize('super_admin', 'admin'),
  auditLog('user_update', 'users'),
  async (req, res) => {
    try {
      const userId = req.params.id;
      const updates = req.body;
      
      // Prevent updating sensitive fields
      delete updates.password;
      delete updates.mfa;
      delete updates.security;
      delete updates._id;

      const user = await User.findById(userId);
      if (!user) {
        return res.status(404).json({ error: 'User not found' });
      }

      // Store original values for audit
      const originalValues = {
        role: user.role,
        status: user.status,
        department: user.department
      };

      // Update user
      Object.assign(user, updates);
      user.audit.updatedBy = req.user._id;
      user.audit.updatedAt = new Date();

      await user.save();

      // Log changes
      const changes = {};
      Object.keys(updates).forEach(key => {
        if (originalValues[key] !== updates[key]) {
          changes[key] = { from: originalValues[key], to: updates[key] };
        }
      });

      await AuditLog.logEvent({
        eventType: 'user_updated',
        userId: req.user._id,
        username: req.user.username,
        userRole: req.user.role,
        targetType: 'user',
        targetId: user._id.toString(),
        targetName: user.username,
        action: 'update',
        resource: 'users',
        description: `Updated user: ${user.username}`,
        changes: { before: originalValues, after: updates },
        ipAddress: req.ip,
        userAgent: req.get('User-Agent'),
        method: req.method,
        endpoint: req.originalUrl,
        severity: 'medium'
      });

      res.json({
        message: 'User updated successfully',
        user: {
          id: user._id,
          username: user.username,
          email: user.email,
          role: user.role,
          status: user.status
        }
      });

    } catch (error) {
      logger.error('Error updating user', { error: error.message, userId: req.user._id });
      res.status(500).json({ error: 'Failed to update user' });
    }
  }
);

// Security Monitoring
router.get('/security/events',
  requirePermission('security', 'read'),
  validatePagination,
  async (req, res) => {
    try {
      const page = req.query.page || 1;
      const limit = req.query.limit || 50;
      const skip = (page - 1) * limit;
      const severity = req.query.severity;
      const eventType = req.query.eventType;
      const timeframe = req.query.timeframe || 24; // hours

      const query = {
        timestamp: { $gte: new Date(Date.now() - timeframe * 60 * 60 * 1000) }
      };

      if (severity) query.severity = severity;
      if (eventType) query.eventType = eventType;

      const [events, total] = await Promise.all([
        AuditLog.find(query)
          .sort({ timestamp: -1 })
          .skip(skip)
          .limit(limit)
          .populate('userId', 'username email role')
          .lean(),
        AuditLog.countDocuments(query)
      ]);

      res.json({
        events,
        pagination: {
          page,
          limit,
          total,
          pages: Math.ceil(total / limit)
        }
      });
    } catch (error) {
      logger.error('Error fetching security events', { error: error.message, userId: req.user._id });
      res.status(500).json({ error: 'Failed to fetch security events' });
    }
  }
);

module.exports = router;
