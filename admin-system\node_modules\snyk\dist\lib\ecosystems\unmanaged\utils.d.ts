import { DepGraphData } from '@snyk/dep-graph';
import { ScanResult } from '../types';
import { DepGraphDataOpenAPI } from './types';
export declare function convertToCamelCase<T>(obj: any): T;
export declare function convertMapCasing<T>(obj: any): T;
export declare function convertObjectArrayCasing<T>(arr: any[]): T[];
export declare function convertDepGraph<T>(depGraphOpenApi: T): DepGraphData;
interface SelfResponse {
    jsonapi: {
        version: string;
    };
    data: {
        type: string;
        id: string;
        attributes: {
            name: string;
            username: string;
            email: string;
            avatar_url: string;
            default_org_context: string;
        };
        links: {
            self: string;
        };
    };
}
export declare function getOrgIdFromSlug(slug: string): Promise<string>;
export declare function getSelf(): Promise<SelfResponse>;
export declare function getOrgDefaultContext(): Promise<string>;
export declare function isUUID(str: any): boolean;
export declare function getOrg(org?: string | null): Promise<string>;
export declare function getUnmanagedDepGraph(scans: {
    [dir: string]: ScanResult[];
}): Promise<DepGraphDataOpenAPI[]>;
export {};
