const path = require('path');
const { logger } = require('../../utils/logger');

/**
 * Configuration Manager
 * Centralizes all configuration management and validation
 */
class ConfigManager {
  constructor() {
    this.config = {};
    this.requiredEnvVars = [
      'JWT_SECRET',
      'SESSION_SECRET',
      'ENCRYPTION_KEY',
      'MONGODB_URI'
    ];
    
    this.loadConfiguration();
  }

  /**
   * Load configuration from environment variables
   */
  loadConfiguration() {
    this.config = {
      // Server Configuration
      server: {
        port: parseInt(process.env.PORT) || 3000,
        host: process.env.HOST || 'localhost',
        environment: process.env.NODE_ENV || 'development',
        appVersion: process.env.APP_VERSION || '1.0.0'
      },

      // Database Configuration
      database: {
        uri: process.env.MONGODB_URI || 'mongodb://localhost:27017/blogwebsite',
        testUri: process.env.MONGODB_TEST_URI || 'mongodb://localhost:27017/blogwebsite_test',
        maxPoolSize: parseInt(process.env.MONGODB_MAX_POOL_SIZE) || 10,
        serverSelectionTimeout: parseInt(process.env.MONGODB_SERVER_SELECTION_TIMEOUT) || 5000,
        socketTimeout: parseInt(process.env.MONGODB_SOCKET_TIMEOUT) || 45000
      },

      // Security Configuration
      security: {
        jwt: {
          secret: process.env.JWT_SECRET,
          expiresIn: process.env.JWT_EXPIRES_IN || '24h',
          issuer: process.env.JWT_ISSUER || 'blog-admin-system',
          audience: process.env.JWT_AUDIENCE || 'blog-admin-users'
        },
        session: {
          secret: process.env.SESSION_SECRET,
          maxAge: parseInt(process.env.SESSION_MAX_AGE) || 24 * 60 * 60 * 1000, // 24 hours
          secure: process.env.NODE_ENV === 'production',
          httpOnly: true,
          sameSite: 'strict'
        },
        encryption: {
          key: process.env.ENCRYPTION_KEY,
          algorithm: process.env.ENCRYPTION_ALGORITHM || 'aes-256-gcm'
        },
        cors: {
          origin: this.parseCorsOrigins(),
          credentials: true,
          methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'],
          allowedHeaders: ['Content-Type', 'Authorization', 'X-CSRF-Token']
        },
        rateLimiting: {
          windowMs: parseInt(process.env.RATE_LIMIT_WINDOW_MS) || 15 * 60 * 1000, // 15 minutes
          maxRequests: parseInt(process.env.RATE_LIMIT_MAX_REQUESTS) || 100,
          skipSuccessfulRequests: false,
          skipFailedRequests: false
        }
      },

      // File Upload Configuration
      upload: {
        maxFileSize: parseInt(process.env.MAX_FILE_SIZE) || 10 * 1024 * 1024, // 10MB
        allowedMimeTypes: (process.env.ALLOWED_MIME_TYPES || 'image/jpeg,image/png,image/gif,image/webp').split(','),
        uploadPath: process.env.UPLOAD_PATH || path.join(__dirname, '../../uploads')
      },

      // Email Configuration
      email: {
        host: process.env.EMAIL_HOST,
        port: parseInt(process.env.EMAIL_PORT) || 587,
        secure: process.env.EMAIL_SECURE === 'true',
        user: process.env.EMAIL_USER,
        password: process.env.EMAIL_PASSWORD,
        from: process.env.EMAIL_FROM || '<EMAIL>'
      },

      // Logging Configuration
      logging: {
        level: process.env.LOG_LEVEL || 'info',
        file: process.env.LOG_FILE || 'logs/app.log',
        maxSize: process.env.LOG_MAX_SIZE || '20m',
        maxFiles: parseInt(process.env.LOG_MAX_FILES) || 5
      },

      // Feature Flags
      features: {
        enableMFA: process.env.ENABLE_MFA === 'true',
        enableAuditLog: process.env.ENABLE_AUDIT_LOG !== 'false',
        enableRateLimiting: process.env.ENABLE_RATE_LIMITING !== 'false',
        enableCSRF: process.env.ENABLE_CSRF !== 'false'
      }
    };
  }

  /**
   * Parse CORS origins from environment variable
   */
  parseCorsOrigins() {
    const origins = process.env.CORS_ORIGINS;
    if (!origins) {
      return process.env.NODE_ENV === 'development' ? true : false;
    }
    
    return origins.split(',').map(origin => origin.trim());
  }

  /**
   * Validate configuration
   */
  async validate() {
    try {
      logger.info('Validating configuration...');

      // Check required environment variables
      this.validateRequiredEnvVars();

      // Validate specific configurations
      this.validateSecurityConfig();
      this.validateDatabaseConfig();
      this.validateServerConfig();

      logger.info('Configuration validation completed successfully');
    } catch (error) {
      logger.error('Configuration validation failed', { error: error.message });
      throw error;
    }
  }

  /**
   * Validate required environment variables
   */
  validateRequiredEnvVars() {
    const missing = this.requiredEnvVars.filter(envVar => !process.env[envVar]);
    
    if (missing.length > 0) {
      throw new Error(`Missing required environment variables: ${missing.join(', ')}`);
    }
  }

  /**
   * Validate security configuration
   */
  validateSecurityConfig() {
    const { jwt, session, encryption } = this.config.security;

    // Validate JWT secret strength
    if (jwt.secret.length < 32) {
      throw new Error('JWT_SECRET must be at least 32 characters long');
    }

    // Validate session secret strength
    if (session.secret.length < 32) {
      throw new Error('SESSION_SECRET must be at least 32 characters long');
    }

    // Validate encryption key
    if (encryption.key.length < 32) {
      throw new Error('ENCRYPTION_KEY must be at least 32 characters long');
    }
  }

  /**
   * Validate database configuration
   */
  validateDatabaseConfig() {
    const { uri } = this.config.database;

    if (!uri.startsWith('mongodb://') && !uri.startsWith('mongodb+srv://')) {
      throw new Error('Invalid MongoDB URI format');
    }
  }

  /**
   * Validate server configuration
   */
  validateServerConfig() {
    const { port } = this.config.server;

    if (port < 1 || port > 65535) {
      throw new Error('Invalid port number');
    }
  }

  /**
   * Get configuration value
   */
  get(path) {
    return this.getNestedValue(this.config, path);
  }

  /**
   * Get nested value from object using dot notation
   */
  getNestedValue(obj, path) {
    return path.split('.').reduce((current, key) => current && current[key], obj);
  }

  /**
   * Get all configuration
   */
  getAll() {
    return { ...this.config };
  }

  /**
   * Check if running in production
   */
  isProduction() {
    return this.config.server.environment === 'production';
  }

  /**
   * Check if running in development
   */
  isDevelopment() {
    return this.config.server.environment === 'development';
  }

  /**
   * Check if running in test
   */
  isTest() {
    return this.config.server.environment === 'test';
  }
}

module.exports = ConfigManager;
