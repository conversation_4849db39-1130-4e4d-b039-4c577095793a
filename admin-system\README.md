# Fortune 500 Admin Panel with Dating Profile System

Enterprise-grade admin panel system with Fortune 500 security standards, comprehensive audit trails, scalable architecture, and integrated dating resume profile management.

## 🔒 Security Features

- **Admin-Only Content Editing** - All content modification restricted to secure admin panel
- **Multi-Factor Authentication (MFA)** with TOTP and backup codes
- **Role-Based Access Control (RBAC)** with granular permissions
- **JWT Authentication** with refresh tokens and session management
- **Strict Access Control** - Public website is completely read-only
- **Request Origin Validation** - Admin panel requests verified by source
- **Rate Limiting** and brute force protection
- **CSRF Protection** and XSS prevention
- **Input Sanitization** and SQL injection prevention
- **Comprehensive Audit Logging** for compliance
- **Real-time Security Monitoring** with automated alerts
- **Encrypted Data Storage** and secure password hashing
- **OWASP Security Standards** implementation

## 🏗️ Architecture

### Technology Stack
- **Backend**: Node.js, Express.js, MongoDB, Redis
- **Frontend**: Vanilla JavaScript, Bootstrap 5, Progressive Web App
- **Security**: Helmet.js, bcrypt, JWT, speakeasy
- **Monitoring**: Prometheus, Grafana, ELK Stack
- **Deployment**: <PERSON><PERSON>, Docker Compose, Nginx

### System Components
- **Authentication Service**: JWT-based auth with MFA
- **User Management**: CRUD operations with role management
- **Content Management**: Version-controlled content with approval workflow
- **Dating Profile System**: Comprehensive dating resume profile management
- **Audit System**: Comprehensive logging and monitoring
- **Security Monitor**: Real-time threat detection and alerting
- **Backup Service**: Automated encrypted backups

## 🚀 Quick Start

### Prerequisites
- Docker and Docker Compose
- 4GB+ RAM
- 10GB+ disk space
- SSL certificates (for production)

### Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd admin-system
   ```

2. **Run the setup script**
   ```bash
   chmod +x scripts/setup.sh
   ./scripts/setup.sh
   ```

3. **Initialize the dating profile**
   ```bash
   npm run init-profile
   ```

4. **Access the system**
   - **Public Website**: https://localhost (read-only content viewing)
   - **Dating Profile**: https://localhost/profile.html (public dating profile)
   - **Admin Panel**: https://localhost/admin (secure content and profile editing)
   - Default admin credentials are in the `.env` file

## 🚫 Content Editing Security

### **IMPORTANT: Admin-Only Editing**

This system implements **strict separation** between content viewing and editing:

- **Public Website** (`https://localhost`):
  - ✅ Read-only access to published content
  - ❌ **NO editing capabilities whatsoever**
  - ❌ All modification attempts are blocked and logged

- **Admin Panel** (`https://localhost/admin`):
  - ✅ **Exclusive content editing access**
  - ✅ Multi-factor authentication required
  - ✅ Role-based permissions enforced
  - ✅ All actions comprehensively audited

### Security Enforcement

- **Server-side blocking** of all public modification attempts
- **Request origin validation** ensures admin panel source
- **Method filtering** blocks POST/PUT/DELETE on public routes
- **Real-time monitoring** of unauthorized access attempts
- **Automatic security alerts** for violation attempts

### Manual Setup

1. **Copy environment file**
   ```bash
   cp .env.example .env
   ```

2. **Generate secrets**
   ```bash
   # Generate secure random keys
   openssl rand -base64 64  # JWT_SECRET
   openssl rand -base64 64  # JWT_REFRESH_SECRET
   openssl rand -base64 64  # SESSION_SECRET
   openssl rand -hex 32     # ENCRYPTION_KEY
   ```

3. **Start services**
   ```bash
   docker-compose up -d
   ```

## 📋 Configuration

### Environment Variables

| Variable | Description | Required | Default |
|----------|-------------|----------|---------|
| `NODE_ENV` | Environment mode | Yes | `production` |
| `PORT` | Application port | No | `3000` |
| `MONGODB_URI` | MongoDB connection string | Yes | - |
| `JWT_SECRET` | JWT signing secret | Yes | - |
| `JWT_REFRESH_SECRET` | JWT refresh token secret | Yes | - |
| `SESSION_SECRET` | Session encryption secret | Yes | - |
| `ENCRYPTION_KEY` | Data encryption key | Yes | - |
| `ADMIN_EMAIL` | Initial admin email | Yes | - |
| `ADMIN_PASSWORD` | Initial admin password | Yes | - |
| `SMTP_HOST` | Email server host | No | - |
| `SMTP_PORT` | Email server port | No | `587` |
| `SMTP_USER` | Email username | No | - |
| `SMTP_PASS` | Email password | No | - |

### Security Configuration

```javascript
// config/security.js
const securityConfig = {
  auth: {
    jwt: {
      expiresIn: '15m',
      refreshExpiresIn: '7d'
    },
    password: {
      minLength: 12,
      requireSpecial: true,
      requireNumbers: true,
      requireUppercase: true,
      requireLowercase: true
    }
  },
  rateLimiting: {
    general: {
      windowMs: 15 * 60 * 1000, // 15 minutes
      max: 100 // requests per window
    },
    login: {
      windowMs: 15 * 60 * 1000,
      max: 5 // login attempts per window
    }
  }
};
```

## 👥 User Management

### Roles and Permissions

| Role | Permissions | Description |
|------|-------------|-------------|
| `super_admin` | All permissions | Full system access |
| `admin` | User, content, security management | Administrative access |
| `editor` | Content creation and editing | Content management |
| `viewer` | Read-only access | View-only access |

### Creating Users

```bash
# Via API
curl -X POST https://localhost/api/admin/users \
  -H "Authorization: Bearer <token>" \
  -H "Content-Type: application/json" \
  -d '{
    "username": "newuser",
    "email": "<EMAIL>",
    "firstName": "John",
    "lastName": "Doe",
    "role": "editor"
  }'
```

## 🔐 Security Procedures

### Multi-Factor Authentication Setup

1. **Enable MFA for user**
   ```javascript
   // Generate MFA secret
   const secret = user.generateMFASecret();
   
   // Generate QR code for authenticator app
   const qrCode = await QRCode.toDataURL(secret.otpauth_url);
   ```

2. **Verify MFA token**
   ```javascript
   const isValid = user.verifyMFAToken(token);
   ```

### Password Security

- Minimum 12 characters
- Must include uppercase, lowercase, numbers, and special characters
- Password history prevents reuse of last 5 passwords
- Automatic lockout after 5 failed attempts

### Session Management

- JWT tokens expire after 15 minutes
- Refresh tokens valid for 7 days
- Automatic session cleanup
- Concurrent session limiting

## 📊 Monitoring and Alerting

### Available Dashboards

- **Grafana**: http://localhost:3001
  - System metrics and performance
  - User activity and security events
  - Application health monitoring

- **Kibana**: http://localhost:5601
  - Log analysis and search
  - Security event correlation
  - Audit trail visualization

- **Prometheus**: http://localhost:9090
  - Metrics collection and alerting
  - Custom metric queries

### Security Alerts

The system automatically monitors for:
- Failed login attempts
- Suspicious activity patterns
- Privilege escalations
- System performance issues
- Security violations

Alerts are sent via:
- Email notifications
- System logs
- Monitoring dashboards

## 🔍 Audit and Compliance

### Audit Trail

All administrative actions are logged with:
- User identification
- Timestamp and IP address
- Action performed
- Before/after values
- Risk assessment

### Compliance Features

- **GDPR**: Data encryption, user consent, right to deletion
- **SOX**: Financial data controls and audit trails
- **HIPAA**: Healthcare data protection (if applicable)
- **ISO 27001**: Information security management

### Log Retention

- Security logs: 7 years
- Audit logs: 7 years
- Application logs: 90 days
- Performance logs: 30 days

## 🛠️ Maintenance

### Backup Procedures

```bash
# Manual backup
docker-compose exec backup /app/backup.sh

# Automated backups run daily at 2 AM
# Retention: 30 days local, 1 year cloud storage
```

### Updates and Patches

1. **Security updates** (immediate)
2. **Feature updates** (monthly)
3. **System maintenance** (quarterly)

### Health Checks

```bash
# Check system health
curl https://localhost/health

# Check service status
docker-compose ps

# View logs
docker-compose logs -f admin-app
```

## 🧪 Testing

### Running Tests

```bash
# Unit tests
npm test

# Security tests
npm run test:security

# Integration tests
npm run test:integration

# Load tests
npm run test:load
```

### Test Coverage

- Unit tests: >90% coverage
- Integration tests: All API endpoints
- Security tests: OWASP Top 10
- Performance tests: Load and stress testing

## 🚨 Troubleshooting

### Common Issues

1. **Cannot connect to database**
   - Check MongoDB container status
   - Verify connection string
   - Check network connectivity

2. **Authentication failures**
   - Verify JWT secrets
   - Check token expiration
   - Review user permissions

3. **High memory usage**
   - Check for memory leaks
   - Review log retention settings
   - Monitor database queries

### Support

For technical support:
- Check logs: `docker-compose logs`
- Review documentation
- Contact system administrator

## 📄 License

This project is proprietary software. All rights reserved.

## 🤝 Contributing

This is an enterprise system. Contributions are managed through:
- Code review process
- Security assessment
- Compliance verification
- Change management procedures

---

## 🔐 **CRITICAL: Admin-Only Content Editing**

### **⚠️ SECURITY NOTICE: Content Editing Restrictions**

This system implements **STRICT SEPARATION** between content viewing and editing:

#### **Public Website Access** (`https://yourdomain.com/`)
- ✅ **Read-only access** to published content
- ❌ **NO editing capabilities** - All modification attempts blocked
- ❌ **NO content management** - Redirects to admin panel
- 🔍 **All unauthorized attempts logged** and monitored

#### **Admin Panel Access** (`https://yourdomain.com/admin`)
- 🔐 **Exclusive editing access** with multi-factor authentication
- 👥 **Role-based permissions** (Super Admin, Admin, Editor, Viewer)
- 📊 **Comprehensive audit trails** for all actions
- 🚨 **Real-time security monitoring** with automated alerts

### **Why This Separation?**

1. **Security**: Prevents unauthorized content modification
2. **Compliance**: Meets enterprise audit requirements
3. **Integrity**: Ensures content quality control
4. **Monitoring**: Tracks all administrative actions

### **For Content Editors**

To edit website content:
1. Navigate to: `https://yourdomain.com/admin`
2. Log in with your admin credentials
3. Complete MFA verification if enabled
4. Use the secure content management interface

**⚠️ Remember**: Content editing is ONLY possible through the admin panel. The public website is completely read-only for security reasons.

---

**🔒 Security Notice**: This system handles sensitive data. Always follow security best practices and keep the system updated with the latest security patches.
