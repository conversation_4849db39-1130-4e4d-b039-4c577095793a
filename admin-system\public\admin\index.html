<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <title>Fortune 500 Admin Panel</title>
    
    <!-- Security Headers -->
    <meta http-equiv="Content-Security-Policy" content="default-src 'self'; script-src 'self' 'unsafe-inline' https://cdn.jsdelivr.net; style-src 'self' 'unsafe-inline' https://cdn.jsdelivr.net; img-src 'self' data: https:; font-src 'self' https://cdn.jsdelivr.net;">
    <meta http-equiv="X-Content-Type-Options" content="nosniff">
    <meta http-equiv="X-Frame-Options" content="DENY">
    <meta http-equiv="X-XSS-Protection" content="1; mode=block">
    <meta name="referrer" content="strict-origin-when-cross-origin">
    
    <!-- Favicon and Icons -->
    <link rel="icon" type="image/x-icon" href="/favicon.ico">
    <link rel="apple-touch-icon" sizes="180x180" href="/apple-touch-icon.png">
    
    <!-- Stylesheets -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet" integrity="sha384-QWTKZyjpPEjISv5WaRU9OFeRpok6YctnYmDr5pNlyT2bRjXh0JMhjY6hW+ALEwIH" crossorigin="anonymous">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.2/font/bootstrap-icons.css" rel="stylesheet">
    <link href="css/admin.css" rel="stylesheet">
</head>
<body class="admin-body">
    <!-- Loading Overlay -->
    <div id="loading-overlay" class="loading-overlay">
        <div class="loading-spinner">
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">Loading...</span>
            </div>
            <div class="loading-text">Loading Admin Panel...</div>
        </div>
    </div>

    <!-- Login Screen -->
    <div id="login-screen" class="login-screen">
        <div class="login-container">
            <div class="login-header">
                <h1><i class="bi bi-shield-lock"></i> Admin Panel</h1>
                <p class="text-muted">Fortune 500 Security Standards</p>
            </div>
            
            <form id="login-form" class="login-form">
                <div class="alert alert-danger d-none" id="login-error"></div>
                
                <div class="mb-3">
                    <label for="email" class="form-label">Email Address</label>
                    <div class="input-group">
                        <span class="input-group-text"><i class="bi bi-envelope"></i></span>
                        <input type="email" class="form-control" id="email" name="email" required autocomplete="username">
                    </div>
                </div>
                
                <div class="mb-3">
                    <label for="password" class="form-label">Password</label>
                    <div class="input-group">
                        <span class="input-group-text"><i class="bi bi-lock"></i></span>
                        <input type="password" class="form-control" id="password" name="password" required autocomplete="current-password">
                        <button class="btn btn-outline-secondary" type="button" id="toggle-password">
                            <i class="bi bi-eye"></i>
                        </button>
                    </div>
                </div>
                
                <div id="mfa-section" class="mb-3 d-none">
                    <label for="mfa-token" class="form-label">
                        <i class="bi bi-shield-check"></i> Multi-Factor Authentication Code
                    </label>
                    <input type="text" class="form-control text-center" id="mfa-token" name="mfaToken" 
                           placeholder="000000" maxlength="6" pattern="[0-9]{6}">
                    <div class="form-text">Enter the 6-digit code from your authenticator app</div>
                </div>
                
                <div class="mb-3 form-check">
                    <input type="checkbox" class="form-check-input" id="remember-me" name="rememberMe">
                    <label class="form-check-label" for="remember-me">
                        Keep me signed in for 7 days
                    </label>
                </div>
                
                <button type="submit" class="btn btn-primary w-100" id="login-btn">
                    <i class="bi bi-box-arrow-in-right"></i> Sign In
                </button>
                
                <div class="login-footer">
                    <a href="#" id="forgot-password-link">Forgot your password?</a>
                </div>
            </form>
        </div>
        
        <!-- Security Notice -->
        <div class="security-notice">
            <div class="container">
                <div class="row">
                    <div class="col-md-12 text-center">
                        <small class="text-muted">
                            <i class="bi bi-shield-check"></i>
                            This system is protected by enterprise-grade security measures.
                            All activities are monitored and logged for security purposes.
                        </small>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Main Admin Interface -->
    <div id="admin-interface" class="admin-interface d-none">
        <!-- Top Navigation -->
        <nav class="navbar navbar-expand-lg navbar-dark bg-dark admin-navbar">
            <div class="container-fluid">
                <a class="navbar-brand" href="#">
                    <i class="bi bi-shield-lock"></i> Admin Panel
                </a>
                
                <!-- User Menu -->
                <div class="navbar-nav ms-auto">
                    <div class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle d-flex align-items-center" href="#" role="button" 
                           data-bs-toggle="dropdown" aria-expanded="false" id="user-menu">
                            <img src="/api/users/avatar" alt="Avatar" class="user-avatar me-2">
                            <span id="user-name">Loading...</span>
                        </a>
                        <ul class="dropdown-menu dropdown-menu-end">
                            <li><a class="dropdown-item" href="#" id="profile-link">
                                <i class="bi bi-person"></i> Profile
                            </a></li>
                            <li><a class="dropdown-item" href="#" id="security-settings-link">
                                <i class="bi bi-shield-check"></i> Security Settings
                            </a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="#" id="logout-link">
                                <i class="bi bi-box-arrow-right"></i> Sign Out
                            </a></li>
                        </ul>
                    </div>
                </div>
            </div>
        </nav>

        <!-- Main Content Area -->
        <div class="admin-main">
            <!-- Sidebar -->
            <nav class="admin-sidebar">
                <div class="sidebar-content">
                    <ul class="nav nav-pills flex-column">
                        <li class="nav-item">
                            <a class="nav-link active" href="#dashboard" data-section="dashboard">
                                <i class="bi bi-speedometer2"></i> Dashboard
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#content" data-section="content">
                                <i class="bi bi-file-text"></i> Content Management
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#dating-profile" data-section="dating-profile">
                                <i class="bi bi-person-heart"></i> Dating Profile
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#users" data-section="users">
                                <i class="bi bi-people"></i> User Management
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#security" data-section="security">
                                <i class="bi bi-shield-lock"></i> Security Monitor
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#audit" data-section="audit">
                                <i class="bi bi-journal-text"></i> Audit Logs
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#settings" data-section="settings">
                                <i class="bi bi-gear"></i> System Settings
                            </a>
                        </li>
                    </ul>
                </div>
            </nav>

            <!-- Content Area -->
            <main class="admin-content">
                <div id="content-container">
                    <!-- Dashboard Section -->
                    <section id="dashboard-section" class="content-section active">
                        <div class="section-header">
                            <h2><i class="bi bi-speedometer2"></i> Dashboard</h2>
                            <div class="section-actions">
                                <button class="btn btn-outline-primary btn-sm" id="refresh-dashboard">
                                    <i class="bi bi-arrow-clockwise"></i> Refresh
                                </button>
                            </div>
                        </div>
                        
                        <!-- Stats Cards -->
                        <div class="row mb-4">
                            <div class="col-md-3">
                                <div class="stat-card">
                                    <div class="stat-icon bg-primary">
                                        <i class="bi bi-people"></i>
                                    </div>
                                    <div class="stat-content">
                                        <h3 id="total-users">-</h3>
                                        <p>Total Users</p>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="stat-card">
                                    <div class="stat-icon bg-success">
                                        <i class="bi bi-file-text"></i>
                                    </div>
                                    <div class="stat-content">
                                        <h3 id="total-content">-</h3>
                                        <p>Content Items</p>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="stat-card">
                                    <div class="stat-icon bg-warning">
                                        <i class="bi bi-shield-exclamation"></i>
                                    </div>
                                    <div class="stat-content">
                                        <h3 id="security-alerts">-</h3>
                                        <p>Security Alerts</p>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="stat-card">
                                    <div class="stat-icon bg-info">
                                        <i class="bi bi-activity"></i>
                                    </div>
                                    <div class="stat-content">
                                        <h3 id="active-sessions">-</h3>
                                        <p>Active Sessions</p>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Recent Activity -->
                        <div class="row">
                            <div class="col-md-8">
                                <div class="card">
                                    <div class="card-header">
                                        <h5><i class="bi bi-clock-history"></i> Recent Activity</h5>
                                    </div>
                                    <div class="card-body">
                                        <div id="recent-activity" class="activity-list">
                                            <div class="text-center text-muted">
                                                <div class="spinner-border spinner-border-sm" role="status">
                                                    <span class="visually-hidden">Loading...</span>
                                                </div>
                                                Loading recent activity...
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="card">
                                    <div class="card-header">
                                        <h5><i class="bi bi-shield-check"></i> Security Status</h5>
                                    </div>
                                    <div class="card-body">
                                        <div id="security-status">
                                            <div class="security-item">
                                                <span class="security-label">System Status</span>
                                                <span class="badge bg-success">Secure</span>
                                            </div>
                                            <div class="security-item">
                                                <span class="security-label">Failed Logins (24h)</span>
                                                <span class="badge bg-warning" id="failed-logins">-</span>
                                            </div>
                                            <div class="security-item">
                                                <span class="security-label">Active Threats</span>
                                                <span class="badge bg-success" id="active-threats">0</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </section>

                    <!-- Other sections will be loaded dynamically -->
                    <section id="content-section" class="content-section">
                        <div class="section-header">
                            <h2><i class="bi bi-file-text"></i> Content Management</h2>
                        </div>
                        <div class="loading-placeholder">
                            <div class="text-center">
                                <div class="spinner-border" role="status">
                                    <span class="visually-hidden">Loading...</span>
                                </div>
                                <p class="mt-2">Loading content management...</p>
                            </div>
                        </div>
                    </section>

                    <!-- Dating Profile Section -->
                    <section id="dating-profile-section" class="content-section">
                        <div class="section-header">
                            <h2><i class="bi bi-person-heart"></i> Dating Profile Management</h2>
                            <div class="section-actions">
                                <button class="btn btn-outline-primary" id="preview-profile-btn">
                                    <i class="bi bi-eye"></i> Preview Profile
                                </button>
                                <button class="btn btn-primary" id="save-profile-btn" disabled>
                                    <i class="bi bi-save"></i> Save Changes
                                </button>
                            </div>
                        </div>

                        <!-- Alert Container -->
                        <div id="alert-container"></div>

                        <!-- Loading Indicator -->
                        <div id="loading-indicator" class="text-center py-3 d-none">
                            <div class="spinner-border spinner-border-sm me-2" role="status"></div>
                            <span>Loading...</span>
                        </div>

                        <!-- Profile Form -->
                        <div class="row">
                            <div class="col-lg-8">
                                <!-- Basic Information -->
                                <div class="card mb-4">
                                    <div class="card-header">
                                        <h5><i class="bi bi-person"></i> Basic Information</h5>
                                    </div>
                                    <div class="card-body">
                                        <div class="row">
                                            <div class="col-md-6">
                                                <div class="mb-3">
                                                    <label for="name" class="form-label">Full Name *</label>
                                                    <input type="text" class="form-control" id="name" required>
                                                    <div class="invalid-feedback"></div>
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="mb-3">
                                                    <label for="age" class="form-label">Age</label>
                                                    <input type="number" class="form-control" id="age" min="18" max="100">
                                                    <div class="invalid-feedback"></div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="mb-3">
                                            <label for="tagline" class="form-label">Tagline *</label>
                                            <input type="text" class="form-control" id="tagline" maxlength="200" required>
                                            <div class="form-text">A catchy one-liner that describes you</div>
                                            <div class="invalid-feedback"></div>
                                        </div>
                                        <div class="mb-3">
                                            <label for="location" class="form-label">Location</label>
                                            <input type="text" class="form-control" id="location" maxlength="100">
                                            <div class="invalid-feedback"></div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Profile Content -->
                                <div class="card mb-4">
                                    <div class="card-header">
                                        <h5><i class="bi bi-file-text"></i> Profile Content</h5>
                                    </div>
                                    <div class="card-body">
                                        <div class="mb-3">
                                            <label for="objective" class="form-label">Objective *</label>
                                            <textarea class="form-control" id="objective" rows="4" maxlength="1000" required></textarea>
                                            <div class="form-text">What are you looking for in a relationship?</div>
                                            <div class="invalid-feedback"></div>
                                        </div>
                                        <div class="mb-3">
                                            <label for="personalSummary" class="form-label">Personal Summary *</label>
                                            <textarea class="form-control" id="personalSummary" rows="5" maxlength="1000" required></textarea>
                                            <div class="form-text">Tell people about yourself</div>
                                            <div class="invalid-feedback"></div>
                                        </div>
                                        <div class="mb-3">
                                            <label for="lookingFor" class="form-label">Looking For *</label>
                                            <textarea class="form-control" id="lookingFor" rows="3" maxlength="500" required></textarea>
                                            <div class="form-text">Describe your ideal partner</div>
                                            <div class="invalid-feedback"></div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="col-lg-4">
                                <!-- Profile Photo -->
                                <div class="card mb-4">
                                    <div class="card-header">
                                        <h5><i class="bi bi-image"></i> Profile Photo</h5>
                                    </div>
                                    <div class="card-body text-center">
                                        <img id="profile-photo-preview" src="" alt="Profile Preview"
                                             class="img-fluid rounded-circle mb-3" style="width: 150px; height: 150px; object-fit: cover; display: none;">
                                        <div class="mb-3">
                                            <label for="profile-photo-url" class="form-label">Photo URL</label>
                                            <input type="url" class="form-control" id="profile-photo-url">
                                        </div>
                                        <div class="mb-3">
                                            <label for="profile-photo-alt" class="form-label">Alt Text</label>
                                            <input type="text" class="form-control" id="profile-photo-alt" placeholder="Profile Photo">
                                        </div>
                                    </div>
                                </div>

                                <!-- Contact Information -->
                                <div class="card mb-4">
                                    <div class="card-header">
                                        <h5><i class="bi bi-envelope"></i> Contact Information</h5>
                                    </div>
                                    <div class="card-body">
                                        <div class="mb-3">
                                            <label for="contact-email" class="form-label">Email *</label>
                                            <input type="email" class="form-control" id="contact-email" required>
                                            <div class="invalid-feedback"></div>
                                        </div>
                                        <div class="mb-3">
                                            <label for="contact-phone" class="form-label">Phone</label>
                                            <input type="tel" class="form-control" id="contact-phone">
                                            <div class="invalid-feedback"></div>
                                        </div>
                                        <hr>
                                        <h6>Social Media</h6>
                                        <div class="mb-3">
                                            <label for="instagram" class="form-label">Instagram</label>
                                            <input type="text" class="form-control" id="instagram" placeholder="username">
                                        </div>
                                        <div class="mb-3">
                                            <label for="linkedin" class="form-label">LinkedIn</label>
                                            <input type="text" class="form-control" id="linkedin" placeholder="username">
                                        </div>
                                        <div class="mb-3">
                                            <label for="twitter" class="form-label">Twitter</label>
                                            <input type="text" class="form-control" id="twitter" placeholder="username">
                                        </div>
                                    </div>
                                </div>

                                <!-- Settings -->
                                <div class="card mb-4">
                                    <div class="card-header">
                                        <h5><i class="bi bi-gear"></i> Settings</h5>
                                    </div>
                                    <div class="card-body">
                                        <div class="mb-3">
                                            <label for="visibility" class="form-label">Visibility</label>
                                            <select class="form-select" id="visibility">
                                                <option value="public">Public</option>
                                                <option value="friends_only">Friends Only</option>
                                                <option value="private">Private</option>
                                            </select>
                                        </div>
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" id="is-active" checked>
                                            <label class="form-check-label" for="is-active">
                                                Profile Active
                                            </label>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </section>
                </div>
            </main>
        </div>
    </div>

    <!-- CSRF Token -->
    <meta name="csrf-token" content="">

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js" integrity="sha384-YvpcrYf0tY3lHB60NNkmXc5s9fDVZLESaAA55NDzOxhy9GkcIdslK1eN7N6jIeHz" crossorigin="anonymous"></script>
    <script src="js/admin.js"></script>
</body>
</html>
