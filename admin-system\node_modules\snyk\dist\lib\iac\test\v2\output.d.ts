import { Ora } from 'ora';
import { TestOutput } from './scan/results';
import { TestCommandResult } from '../../../../cli/commands/types';
import { CustomError, FormattedCustomError } from '../../../errors';
import { SnykIacTestError } from './errors';
export declare function buildOutput({ scanResult, testSpinner, options, iacNewEngine, }: {
    scanResult: TestOutput;
    testSpinner?: Ora;
    options: any;
    iacNewEngine?: boolean;
}): TestCommandResult;
interface ResponseData {
    response: string;
    json: string;
    sarif: string;
}
export declare class NoSuccessfulScansError extends FormattedCustomError {
    json: string | undefined;
    jsonStringifiedResults: string | undefined;
    sarifStringifiedResults: string | undefined;
    fields: {
        path: string;
    } & Record<string, string>;
    constructor(responseData: Omit<ResponseData, 'response'>, errors: SnykIacTestError[], options: {
        json?: boolean;
        sarif?: boolean;
    });
    get path(): string;
    set path(path1: string);
}
export declare class NoLoadableInputError extends NoSuccessfulScansError {
    constructor(responseData: Omit<ResponseData, 'response'>, errors: SnykIacTestError[], options: {
        json?: boolean;
        sarif?: boolean;
    });
}
export declare class FoundIssuesError extends CustomError {
    jsonStringifiedResults: string;
    sarifStringifiedResults: string;
    constructor(responseData: ResponseData);
}
export {};
